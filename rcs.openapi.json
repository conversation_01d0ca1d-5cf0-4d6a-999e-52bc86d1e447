{"openapi": "3.0.1", "info": {"title": "rcs", "description": "", "version": "1.0.0"}, "tags": [], "paths": {"/db/site_info": {"get": {"summary": "查询所有site_info", "deprecated": false, "description": "", "tags": [], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "object", "properties": {"dir": {"type": "integer"}, "floor": {"type": "integer"}, "height": {"type": "string"}, "id": {"type": "integer"}, "layer": {"type": "integer"}, "name": {"type": "string"}, "operation": {"type": "integer"}, "other": {"type": "string"}, "site_nature": {"type": "integer"}, "site_point": {"type": "string"}, "site_point_name": {"type": "string"}, "site_point_valid": {"type": "integer"}, "site_theta": {"type": "number"}}, "required": ["dir", "floor", "height", "id", "layer", "name", "operation", "other", "site_nature", "site_point", "site_point_name", "site_point_valid", "site_theta"]}, "status": {"type": "string"}}, "required": ["data", "status"]}}}, "headers": {}}}, "security": []}, "post": {"summary": "添加site_info", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": {"dir": 0, "floor": 21, "height": "0,0,0", "layer": 0, "name": "工位1", "operation": 30, "other": "0", "site_nature": 0, "site_point": "0,0", "site_point_name": "Point-49", "site_point_valid": 0, "site_theta": 4.71}}}}, "responses": {"501": {"description": "", "content": {"*/*": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": []}, "put": {"summary": "更新单条siteinfo", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": {"id": 0, "dir": 0, "floor": 21, "height": "0,0,0", "layer": 0, "name": "工位11", "operation": 30, "other": "0", "site_nature": 0, "site_point": "0,0", "site_point_name": "Point-49", "site_point_valid": 0, "site_theta": 4.71}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": []}}, "/db/site_info/{id}": {"get": {"summary": "根据id查询单条site_info", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "example": "2", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "object", "properties": {"dir": {"type": "integer"}, "floor": {"type": "integer"}, "height": {"type": "string"}, "id": {"type": "integer"}, "layer": {"type": "integer"}, "name": {"type": "string"}, "operation": {"type": "integer"}, "other": {"type": "string"}, "site_nature": {"type": "integer"}, "site_point": {"type": "string"}, "site_point_name": {"type": "string"}, "site_point_valid": {"type": "integer"}, "site_theta": {"type": "number"}}, "required": ["dir", "floor", "height", "id", "layer", "name", "operation", "other", "site_nature", "site_point", "site_point_name", "site_point_valid", "site_theta"]}, "status": {"type": "string"}}, "required": ["data", "status"]}}}, "headers": {}}}, "security": []}, "delete": {"summary": "根据id删除单条site_info", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "id", "in": "path", "description": "0", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "string"}, "status": {"type": "string"}}, "required": ["data", "status"]}}}, "headers": {}}}, "security": []}}, "/db/path_point": {"get": {"summary": "获取所有pathpoint数据", "deprecated": false, "description": "", "tags": [], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"floor": {"type": "integer"}, "id": {"type": "integer"}, "is_rotated_in_place": {"type": "integer"}, "name": {"type": "string"}, "pose": {"type": "string"}, "property": {"type": "integer"}}, "required": ["floor", "id", "is_rotated_in_place", "name", "pose", "property"]}}, "status": {"type": "string"}}, "required": ["data", "status"]}}}, "headers": {}}}, "security": []}, "post": {"summary": "新增pathpoint", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": {"floor": 2, "is_rotated_in_place": 0, "name": "Point-89", "pose": {"x": 7.355, "y": -1.344}, "property": 0}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": []}, "put": {"summary": "更新单个pathpoint", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": {"floor": 1, "id": 10232, "is_rotated_in_place": 0, "name": "Point-1", "pose": {"x": 7.355, "y": -1.344}, "property": 0}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "string"}, "status": {"type": "string"}}, "required": ["data", "status"]}}}, "headers": {}}}, "security": []}}, "/db/path_point/{id}": {"get": {"summary": "获取单个pathpoint", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "example": "10292", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "object", "properties": {"floor": {"type": "integer"}, "id": {"type": "integer"}, "is_rotated_in_place": {"type": "integer"}, "name": {"type": "string"}, "pose": {"type": "string"}, "property": {"type": "integer"}}, "required": ["floor", "id", "is_rotated_in_place", "name", "pose", "property"]}, "status": {"type": "string"}}, "required": ["data", "status"]}}}, "headers": {}}}, "security": []}, "delete": {"summary": "删除指定id的pathpoint", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "example": "10291", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "string"}, "status": {"type": "string"}}, "required": ["data", "status"]}}}, "headers": {}}}, "security": []}}, "/publish/resetControlTwist": {"get": {"summary": "重置小车速度", "deprecated": false, "description": "", "tags": [], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": []}}, "/service/setSoftManaulAutoFlag": {"get": {"summary": "设置手自动模式", "deprecated": false, "description": "flag 为 true时 小车手动\n为false时 小车自动", "tags": [], "parameters": [{"name": "flag", "in": "query", "description": "", "required": false, "example": "true", "schema": {"type": "string"}}], "responses": {"500": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "status": {"type": "string"}}, "required": ["message", "status"]}}}, "headers": {}}}, "security": []}}, "/publish/setControlTwist": {"post": {"summary": "控制小车速度接口", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"axis": {"type": "string", "description": "值为vx vy vz"}, "delta": {"type": "string", "description": "值为0.5或-0.5"}}, "required": ["axis", "delta"]}, "example": {"axis": "vz", "delta": 0.5}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": []}}, "/publish/taskControl": {"post": {"summary": "publish_command", "deprecated": false, "description": "接口支持{\"awake\", \"clear\", \"pause\", \"stop\"}四种命令，\n请求体为\n{\n    \"command\": \"awake\"\n}\n响应体为\n{\n    \"status\": \"success\" // error\n}", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"command": {"type": "string"}}, "required": ["command"]}, "example": {"command": "awake"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string"}}, "required": ["status"]}}}, "headers": {}}}, "security": []}}, "/service/taskStatusQuery": {"get": {"summary": "查询任务状态", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "uuid", "in": "query", "description": "", "required": false, "example": "abc", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": []}}, "/db/path_route": {"get": {"summary": "获取所有path_route", "deprecated": false, "description": "", "tags": [], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"bezier_t": {"type": "integer"}, "c1": {"type": "string"}, "c2": {"type": "string"}, "e": {"type": "string"}, "floor": {"type": "integer"}, "id": {"type": "integer"}, "is_movement": {"type": "integer"}, "max_speed": {"type": "integer"}, "name": {"type": "string"}, "property": {"type": "string"}, "s": {"type": "string"}, "type": {"type": "string"}, "width": {"type": "integer"}}, "required": ["bezier_t", "c1", "c2", "e", "floor", "id", "is_movement", "max_speed", "name", "property", "s", "type", "width"]}}, "status": {"type": "string"}}, "required": ["data", "status"]}}}, "headers": {}}}, "security": []}, "post": {"summary": "新增一条pathroute", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": {"bezier_t": 0, "c1": {"x": 0, "y": 0}, "c2": {"x": 0, "y": 0}, "e": "Point-8", "floor": 2, "is_movement": 0, "max_speed": 0, "name": "Route-75", "property": "double", "s": "Point-9", "type": "straight_line", "width": 0}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "string"}, "status": {"type": "string"}}, "required": ["data", "status"]}}}, "headers": {}}}, "security": []}, "put": {"summary": "更新单个path_route", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": {"bezier_t": 0, "c1": {"x": 0, "y": 0}, "c2": {"x": 0, "y": 0}, "e": "Point-8", "floor": 2, "is_movement": 1, "max_speed": 0, "name": "Route-75", "property": "double", "s": "Point-9", "type": "straight_line", "width": 0, "id": 53}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "string"}, "status": {"type": "string"}}, "required": ["data", "status"]}}}, "headers": {}}}, "security": []}}, "/db/path_route/{id}": {"get": {"summary": "根据id获取单个pathroute", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "example": "1", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "object", "properties": {"bezier_t": {"type": "integer"}, "c1": {"type": "string"}, "c2": {"type": "string"}, "e": {"type": "string"}, "floor": {"type": "integer"}, "id": {"type": "integer"}, "is_movement": {"type": "integer"}, "max_speed": {"type": "integer"}, "name": {"type": "string"}, "property": {"type": "string"}, "s": {"type": "string"}, "type": {"type": "string"}, "width": {"type": "integer"}}, "required": ["bezier_t", "c1", "c2", "e", "floor", "id", "is_movement", "max_speed", "name", "property", "s", "type", "width"]}, "status": {"type": "string"}}, "required": ["data", "status"]}}}, "headers": {}}}, "security": []}, "delete": {"summary": "删除单个pathroute", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "example": "53", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "string"}, "status": {"type": "string"}}, "required": ["data", "status"]}}}, "headers": {}}}, "security": []}}, "/v2/api/ping": {"get": {"summary": "ping", "deprecated": false, "description": "", "tags": [], "parameters": [], "responses": {"200": {"description": "", "content": {"*/*": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": []}}}, "components": {"schemas": {}, "securitySchemes": {}}, "servers": [], "security": []}