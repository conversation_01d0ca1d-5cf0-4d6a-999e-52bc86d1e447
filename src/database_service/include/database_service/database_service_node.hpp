#ifndef DATABASE_SERVICE__DATABASE_SERVICE_NODE_HPP_
#define DATABASE_SERVICE__DATABASE_SERVICE_NODE_HPP_

#include <rclcpp/rclcpp.hpp>
#include <database_interfaces/srv/query_path_point.hpp>
#include <database_interfaces/srv/query_path_route.hpp>
#include <database_interfaces/srv/query_site_info.hpp>
#include <database_interfaces/srv/query_special_site.hpp>
#include <database_interfaces/srv/query_task_info.hpp>
#include <database_interfaces/srv/query_layer_switch_point.hpp>
#include <database_interfaces/srv/query_layer_switch_connection.hpp>
#include <database_interfaces/srv/query_layer_switch_connection_info.hpp>
#include <database_interfaces/srv/query_area_info.hpp>
#include <memory>
#include "database_service/database_manager.hpp"

namespace database_service
{

class DatabaseServiceNode : public rclcpp::Node
{
  public:
  DatabaseServiceNode();

  private:
  std::shared_ptr<DatabaseManager> db_manager_;

  rclcpp::Service<database_interfaces::srv::QueryPathPoint>::SharedPtr
      path_point_service_;
  rclcpp::Service<database_interfaces::srv::QueryPathRoute>::SharedPtr
      path_route_service_;
  rclcpp::Service<database_interfaces::srv::QuerySiteInfo>::SharedPtr
      site_info_service_;
  rclcpp::Service<database_interfaces::srv::QueryAreaInfo>::SharedPtr
      area_info_service_;
  rclcpp::Service<database_interfaces::srv::QuerySpecialSite>::SharedPtr
      special_site_service_;
  rclcpp::Service<database_interfaces::srv::QueryTaskInfo>::SharedPtr
      task_info_service_;
  rclcpp::Service<database_interfaces::srv::QueryLayerSwitchPoint>::SharedPtr
      layer_switch_point_service_;
  rclcpp::Service<database_interfaces::srv::QueryLayerSwitchConnection>::
      SharedPtr layer_switch_connection_service_;
  rclcpp::Service<database_interfaces::srv::QueryLayerSwitchConnectionInfo>::
      SharedPtr layer_switch_connection_info_service_;

  void handle_query_path_point(
      const std::shared_ptr<database_interfaces::srv::QueryPathPoint::Request>
          request,
      std::shared_ptr<database_interfaces::srv::QueryPathPoint::Response>
          response);

  void handle_query_path_route(
      const std::shared_ptr<database_interfaces::srv::QueryPathRoute::Request>
          request,
      std::shared_ptr<database_interfaces::srv::QueryPathRoute::Response>
          response);

  void handle_query_site_info(
      const std::shared_ptr<database_interfaces::srv::QuerySiteInfo::Request>
          request,
      std::shared_ptr<database_interfaces::srv::QuerySiteInfo::Response>
          response);

  void handle_query_area_info(
      const std::shared_ptr<database_interfaces::srv::QueryAreaInfo::Request>
          request,
      std::shared_ptr<database_interfaces::srv::QueryAreaInfo::Response>
          response);

  void handle_query_special_site(
      const std::shared_ptr<database_interfaces::srv::QuerySpecialSite::Request>
          request,
      std::shared_ptr<database_interfaces::srv::QuerySpecialSite::Response>
          response);

  void handle_query_task_info(
      const std::shared_ptr<database_interfaces::srv::QueryTaskInfo::Request>
          request,
      std::shared_ptr<database_interfaces::srv::QueryTaskInfo::Response>
          response);
};

}  // namespace database_service

#endif  // DATABASE_SERVICE__DATABASE_SERVICE_NODE_HPP_
