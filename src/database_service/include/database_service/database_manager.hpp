#ifndef DATABASE_SERVICE__DATABASE_MANAGER_HPP_
#define DATABASE_SERVICE__DATABASE_MANAGER_HPP_

#include <sqlite3.h>
#include <string>
#include <vector>
#include <glog/logging.h>
#include "database_service/database_types.hpp"

namespace database_service
{

class DatabaseManager
{
  public:
  DatabaseManager(const std::string& db_path);
  ~DatabaseManager();

  bool initialize();

  // 路径点查询
  std::vector<PathPoint> query_path_points(int id = 0, int floor = 0);

  // 路径路线查询
  std::vector<PathRoute> query_path_routes(int id = 0, int floor = 0);

  // 站点信息查询
  std::vector<SiteInfo> query_site_info(int id = 0, int floor = 0);

  // 区域信息查询
  std::vector<AreaInfo> query_area_info(int id = 0, int floor = 0);

  // 特殊站点查询
  std::vector<SpecialSite> query_special_sites(int id = 0, int floor = 0,
                                               int site_nature = -1);

  // 任务信息查询（联查site_info和path_point表）
  std::vector<TaskInfo> query_task_info(int id = 0, int floor = 0);

  // 楼层切换点查询
  std::vector<LayerSwitchPoint> query_layer_switch_points(int id = 0,
                                                          int floor = 0,
                                                          int point_id = 0);

  // 楼层切换连接查询
  std::vector<LayerSwitchConnection> query_layer_switch_connections(
      int from_switch_path_point_id = 0, int to_switch_path_point_id = 0);

  // 楼层切换连接信息查询
  std::vector<LayerSwitchConnectionInfo> query_layer_switch_connection_info(
      int path_point_id = 0, int floor = 0);

  private:
  std::string db_path_;
  sqlite3* db_;

  bool create_tables();
  std::vector<Point2D> parse_area_points(const std::string& area_points_id);

  // 从点名中提取数字部分，如 "Point-10001" -> 10001
  int extract_point_number(const std::string& point_name);
};

}  // namespace database_service

#endif  // DATABASE_SERVICE__DATABASE_MANAGER_HPP_
