#ifndef DATABASE_SERVICE__DATABASE_TYPES_HPP_
#define DATABASE_SERVICE__DATABASE_TYPES_HPP_

#include <string>
#include <vector>
#include <cstdint>

namespace database_service
{

// 路径点属性枚举
enum class PointProperty : uint8_t
{
  PRI_POINT = 0,        // 主节点
  SUB_POINT = 1,        // 子节点
  SUB_ENTER_POINT = 2   // 子节点入口
};

// 路径路线类型枚举
enum class RouteType : uint8_t
{
  ROUTE_LINE = 0,   // 直线
  ROUTE_CURVE = 1   // 曲线
};

// 路径路线属性枚举
enum class RouteProperty : uint8_t
{
  SINGLE = 0,   // 单向
  DOUBLE = 1    // 双向
};

// 站点属性枚举
enum class SiteProperty : uint8_t
{

};

inline std::string to_string(PointProperty prop)
{
  switch (prop)
  {
    case PointProperty::PRI_POINT:
      return "PRI_POINT";
    case PointProperty::SUB_POINT:
      return "SUB_POINT";
    case PointProperty::SUB_ENTER_POINT:
      return "SUB_ENTER_POINT";
    default:
      return "UNKNOWN";
  }
}

inline std::string to_string(RouteType type)
{
  switch (type)
  {
    case RouteType::ROUTE_LINE:
      return "ROUTE_LINE";
    case RouteType::ROUTE_CURVE:
      return "ROUTE_CURVE";
    default:
      return "UNKNOWN";
  }
}

inline std::string to_string(RouteProperty prop)
{
  switch (prop)
  {
    case RouteProperty::SINGLE:
      return "SINGLE";
    case RouteProperty::DOUBLE:
      return "DOUBLE";
    default:
      return "UNKNOWN";
  }
}

// 2D点结构体
struct Point2D
{
  double x;
  double y;
};

// 路径点结构体
struct PathPoint
{
  int id;
  std::string name;
  double x;
  double y;
  PointProperty point_property;
  bool is_rotated_in_place;
  int floor;
};

// 路径路线结构体
struct PathRoute
{
  int id;
  std::string name;
  RouteType type;
  int start;
  int end;
  double c1_x;
  double c1_y;
  double c2_x;
  double c2_y;
  double length;
  RouteProperty route_property;
  int floor;
  float width;
  bool movement;
  float max_speed;
};

// 站点信息结构体
struct SiteInfo
{
  int id;
  std::string name;
  int point_id;
  double site_theta;
  int floor;
  std::string operation;
  bool dir;
  double param1;
  double param2;
  double param3;
  double site_point_x;
  double site_point_y;
  bool site_point_valid;
  std::string other;
  int layer;
};

// 区域信息结构体
struct AreaInfo
{
  int id;
  int area_mode;
  std::vector<Point2D> area_points;
  int area_param;
  int floor;
};

// 特殊站点结构体
struct SpecialSite
{
  int id;
  std::string name;
  int site_nature;
  std::string site_point_name;
  double site_theta;
  int floor;
};

// 任务信息结构体（包含关联的路径点信息）
struct TaskInfo
{
  // 任务信息（来自site_info表）
  int id;
  std::string name;
  int point_id;
  double site_theta;
  int floor;
  std::string operation;
  bool dir;
  double param1;
  double param2;
  double param3;
  double site_point_x;
  double site_point_y;
  bool site_point_valid;
  std::string other;
  int layer;

  // 关联的路径点信息（来自path_point表）
  std::string point_name;
  double point_x;
  double point_y;
  int point_floor;
  PointProperty point_property;
  bool point_is_rotated_in_place;
};

// 楼层切换点结构体
struct LayerSwitchPoint
{
  int id;
  std::string name;
  int nature;
  int point_id;
  int floor;
  double in_theta;
  double out_theta;
  bool is_active;
};

// 楼层切换连接结构体
struct LayerSwitchConnection
{
  int id;
  int from_switch_path_point_id;
  int to_switch_path_point_id;
  bool is_active;
};

// 可到达点结构体
struct ReachablePoint
{
  int point_id;
  int point_number;
  double in_theta;
  int floor;
};

// 楼层切换连接信息结构体
struct LayerSwitchConnectionInfo
{
  // 起始点信息
  std::string source_name;
  int source_point_id;
  int source_point_number;
  int source_floor;
  double source_out_theta;

  // 可到达的点列表
  std::vector<ReachablePoint> reachable_points;
};

}  // namespace database_service

#endif  // DATABASE_SERVICE__DATABASE_TYPES_HPP_
