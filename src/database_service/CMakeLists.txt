cmake_minimum_required(VERSION 3.8)
project(database_service)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(database_interfaces REQUIRED)
find_package(PkgConfig REQUIRED)
pkg_check_modules(SQLITE3 REQUIRED sqlite3)
find_package(PkgConfig REQUIRED)
pkg_check_modules(GLOG REQUIRED IMPORTED_TARGET libglog)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  # the following line skips the linter which checks for copyrights
  # comment the line when a copyright and license is added to all source files
  set(ament_cmake_copyright_FOUND TRUE)
  # the following line skips cpplint (only works in a git repo)
  # comment the line when this package is in a git repo and when
  # a copyright and license is added to all source files
  set(ament_cmake_cpplint_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
endif()

# Include directories
include_directories(include)
include_directories(${SQLITE3_INCLUDE_DIRS})

add_executable(database_service_node
  src/main.cpp
  src/database_service_node.cpp
  src/database_manager.cpp
)

ament_target_dependencies(database_service_node
  rclcpp
  database_interfaces
)

target_link_directories(database_service_node PRIVATE ${SQLITE3_LIBRARY_DIRS})
target_compile_options(database_service_node PRIVATE ${SQLITE3_CFLAGS_OTHER})

target_link_libraries(database_service_node
  ${SQLITE3_LIBRARIES}
  PkgConfig::GLOG
)

install(TARGETS database_service_node
  DESTINATION lib/${PROJECT_NAME})

ament_package()
