#include "database_service/database_manager.hpp"
#include <sstream>
#include <iostream>
#include <stdexcept>
#include <map>
#include <glog/logging.h>

namespace database_service
{

DatabaseManager::DatabaseManager(const std::string& db_path)
    : db_path_(db_path), db_(nullptr)
{
}

DatabaseManager::~DatabaseManager()
{
  if (db_)
  {
    sqlite3_close(db_);
  }
}

bool DatabaseManager::initialize()
{
  LOG(INFO) << "初始化数据库连接: " << db_path_;

  int rc = sqlite3_open(db_path_.c_str(), &db_);
  if (rc != SQLITE_OK)
  {
    LOG(ERROR) << "无法打开数据库: " << sqlite3_errmsg(db_);
    return false;
  }

  if (!create_tables())
  {
    LOG(ERROR) << "创建数据库表失败";
    return false;
  }

  LOG(INFO) << "数据库初始化成功";
  return true;
}

bool DatabaseManager::create_tables()
{
  const char* create_path_point_sql = R"(
    CREATE TABLE IF NOT EXISTS path_point (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      x REAL NOT NULL,
      y REAL NOT NULL,
      property INTEGER NOT NULL,
      is_rotated_in_place INTEGER NOT NULL,
      floor INTEGER NOT NULL
    );
  )";

  const char* create_path_route_sql = R"(
    CREATE TABLE IF NOT EXISTS path_route (
      id INTEGER PRIMARY KEY,
      name TEXT NOT NULL UNIQUE,
      type TEXT NOT NULL,
      start INTEGER NOT NULL,
      end INTEGER NOT NULL,
      c1_x REAL NOT NULL,
      c1_y REAL NOT NULL,
      c2_x REAL NOT NULL,
      c2_y REAL NOT NULL,
      length REAL NOT NULL,
      property INTEGER NOT NULL,
      floor INTEGER NOT NULL,
      width REAL NOT NULL,
      is_movement INTEGER NOT NULL,
      max_speed REAL,
      FOREIGN KEY(start) REFERENCES path_point(id),
      FOREIGN KEY(end) REFERENCES path_point(id)
    );
  )";

  const char* create_site_info_sql = R"(
    CREATE TABLE IF NOT EXISTS site_info (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      point_id INTEGER NOT NULL,
      site_theta REAL NOT NULL,
      floor INTEGER NOT NULL,
      operation TEXT NOT NULL,
      dir INTEGER NOT NULL,
      param1 REAL NOT NULL,
      param2 REAL NOT NULL,
      param3 REAL NOT NULL,
      site_point_x REAL NOT NULL,
      site_point_y REAL NOT NULL,
      site_point_valid INTEGER NOT NULL,
      other TEXT NOT NULL,
      layer INTEGER NOT NULL,
      FOREIGN KEY(point_id) REFERENCES path_point(id)
    );
  )";

  const char* create_area_info_sql = R"(
    CREATE TABLE IF NOT EXISTS area_info (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      area_mode INTEGER NOT NULL,
      area_points_id TEXT NOT NULL,
      area_param INTEGER NOT NULL,
      floor INTEGER NOT NULL
    );
  )";

  const char* create_area_point_tb_sql = R"(
    CREATE TABLE IF NOT EXISTS area_point_tb (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      x REAL NOT NULL,
      y REAL NOT NULL
    );
  )";

  const char* create_special_site_tb_sql = R"(
    CREATE TABLE IF NOT EXISTS special_site_tb (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      site_nature INTEGER NOT NULL,
      site_point_name TEXT NOT NULL,
      site_theta REAL NOT NULL DEFAULT 0,
      floor INTEGER NOT NULL
    );
  )";

  const char* create_layer_switch_point_sql = R"(
    CREATE TABLE IF NOT EXISTS layer_switch_point (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      nature INTEGER NOT NULL,
      point_id INTEGER NOT NULL,
      floor INTEGER NOT NULL,
      in_theta REAL NOT NULL DEFAULT 0,
      out_theta REAL NOT NULL DEFAULT 0,
      is_active INTEGER NOT NULL DEFAULT 1,
      FOREIGN KEY(point_id) REFERENCES path_point(id),
      UNIQUE(point_id)
    );
  )";

  const char* create_layer_switch_connection_sql = R"(
    CREATE TABLE IF NOT EXISTS layer_switch_connection (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      from_switch_id INTEGER NOT NULL,
      to_switch_id INTEGER NOT NULL,
      is_active INTEGER NOT NULL DEFAULT 1,
      FOREIGN KEY(from_switch_id) REFERENCES layer_switch_point(id),
      FOREIGN KEY(to_switch_id) REFERENCES layer_switch_point(id),
      UNIQUE(from_switch_id, to_switch_id)
    );
  )";

  // 创建所有表
  const char* sqls[] = {
      create_path_point_sql,         create_path_route_sql,
      create_site_info_sql,          create_area_info_sql,
      create_area_point_tb_sql,      create_special_site_tb_sql,
      create_layer_switch_point_sql, create_layer_switch_connection_sql};

  for (const char* sql : sqls)
  {
    char* err_msg = nullptr;
    int rc = sqlite3_exec(db_, sql, nullptr, nullptr, &err_msg);
    if (rc != SQLITE_OK)
    {
      LOG(ERROR) << "创建表失败: " << err_msg;
      sqlite3_free(err_msg);
      return false;
    }
  }

  return true;
}

std::vector<PathPoint> DatabaseManager::query_path_points(int id, int floor)
{
  std::vector<PathPoint> results;

  std::string sql =
      "SELECT id, name, x, y, property, is_rotated_in_place, floor FROM "
      "path_point WHERE 1=1";
  if (id != 0)
  {
    sql += " AND id = " + std::to_string(id);
  }
  if (floor != 0)
  {
    sql += " AND floor = " + std::to_string(floor);
  }

  sqlite3_stmt* stmt;
  int rc = sqlite3_prepare_v2(db_, sql.c_str(), -1, &stmt, nullptr);
  if (rc != SQLITE_OK)
  {
    std::string error_msg =
        "准备路径点查询语句失败: " + std::string(sqlite3_errmsg(db_));
    LOG(ERROR) << error_msg;
    throw std::runtime_error(error_msg);
  }

  while (sqlite3_step(stmt) == SQLITE_ROW)
  {
    PathPoint point;
    point.id = sqlite3_column_int(stmt, 0);
    point.name = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 1));
    point.x = sqlite3_column_double(stmt, 2);
    point.y = sqlite3_column_double(stmt, 3);
    point.point_property =
        static_cast<PointProperty>(sqlite3_column_int(stmt, 4));
    point.is_rotated_in_place = sqlite3_column_int(stmt, 5) != 0;
    point.floor = sqlite3_column_int(stmt, 6);
    results.push_back(point);
  }

  sqlite3_finalize(stmt);
  VLOG(1) << "查询到 " << results.size() << " 个路径点";
  return results;
}

std::vector<PathRoute> DatabaseManager::query_path_routes(int id, int floor)
{
  std::vector<PathRoute> results;

  std::string sql =
      "SELECT id, name, type, start, end, c1_x, c1_y, c2_x, c2_y, length, "
      "property, "
      "floor, width, is_movement, max_speed FROM "
      "path_route WHERE 1=1";
  if (id != 0)
  {
    sql += " AND id = " + std::to_string(id);
  }
  if (floor != 0)
  {
    sql += " AND floor = " + std::to_string(floor);
  }

  sqlite3_stmt* stmt;
  int rc = sqlite3_prepare_v2(db_, sql.c_str(), -1, &stmt, nullptr);
  if (rc != SQLITE_OK)
  {
    std::string error_msg =
        "准备路径路线查询语句失败: " + std::string(sqlite3_errmsg(db_));
    LOG(ERROR) << error_msg;
    throw std::runtime_error(error_msg);
  }

  while (sqlite3_step(stmt) == SQLITE_ROW)
  {
    PathRoute route;
    route.id = sqlite3_column_int(stmt, 0);
    route.name = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 1));
    route.type = static_cast<RouteType>(sqlite3_column_int(stmt, 2));
    route.start = sqlite3_column_int(stmt, 3);
    route.end = sqlite3_column_int(stmt, 4);
    route.c1_x = sqlite3_column_double(stmt, 5);
    route.c1_y = sqlite3_column_double(stmt, 6);
    route.c2_x = sqlite3_column_double(stmt, 7);
    route.c2_y = sqlite3_column_double(stmt, 8);
    route.length = sqlite3_column_double(stmt, 9);
    route.route_property =
        static_cast<RouteProperty>(sqlite3_column_int(stmt, 10));
    route.floor = sqlite3_column_int(stmt, 11);
    route.width = static_cast<float>(sqlite3_column_double(stmt, 12));
    route.movement = sqlite3_column_int(stmt, 13) != 0;
    route.max_speed = static_cast<float>(sqlite3_column_double(stmt, 14));
    results.push_back(route);
  }

  sqlite3_finalize(stmt);
  VLOG(1) << "查询到 " << results.size() << " 个路径路线";
  return results;
}

std::vector<SiteInfo> DatabaseManager::query_site_info(int id, int floor)
{
  std::vector<SiteInfo> results;

  std::string sql =
      "SELECT id, name, point_id, site_theta, floor, "
      "operation, dir, param1, param2, param3, site_point_x, site_point_y, "
      "site_point_valid, other, layer FROM site_info WHERE 1=1";
  if (id != 0)
  {
    sql += " AND id = " + std::to_string(id);
  }
  if (floor != 0)
  {
    sql += " AND floor = " + std::to_string(floor);
  }

  sqlite3_stmt* stmt;
  int rc = sqlite3_prepare_v2(db_, sql.c_str(), -1, &stmt, nullptr);
  if (rc != SQLITE_OK)
  {
    std::string error_msg =
        "准备站点信息查询语句失败: " + std::string(sqlite3_errmsg(db_));
    LOG(ERROR) << error_msg;
    throw std::runtime_error(error_msg);
  }

  while (sqlite3_step(stmt) == SQLITE_ROW)
  {
    SiteInfo site;
    site.id = sqlite3_column_int(stmt, 0);
    site.name = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 1));
    site.point_id = sqlite3_column_int(stmt, 2);
    site.site_theta = sqlite3_column_double(stmt, 3);
    site.floor = sqlite3_column_int(stmt, 4);
    site.operation =
        reinterpret_cast<const char*>(sqlite3_column_text(stmt, 5));
    site.dir = sqlite3_column_int(stmt, 6) != 0;
    site.param1 = sqlite3_column_double(stmt, 7);
    site.param2 = sqlite3_column_double(stmt, 8);
    site.param3 = sqlite3_column_double(stmt, 9);
    site.site_point_x = sqlite3_column_double(stmt, 10);
    site.site_point_y = sqlite3_column_double(stmt, 11);
    site.site_point_valid = sqlite3_column_int(stmt, 12) != 0;
    site.other = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 13));
    site.layer = sqlite3_column_int(stmt, 14);
    results.push_back(site);
  }

  sqlite3_finalize(stmt);
  VLOG(1) << "查询到 " << results.size() << " 个站点信息";
  return results;
}

std::vector<AreaInfo> DatabaseManager::query_area_info(int id, int floor)
{
  std::vector<AreaInfo> results;

  std::string sql =
      "SELECT id, area_mode, area_points_id, area_param, floor FROM area_info "
      "WHERE 1=1";
  if (id != 0)
  {
    sql += " AND id = " + std::to_string(id);
  }
  if (floor != 0)
  {
    sql += " AND floor = " + std::to_string(floor);
  }

  sqlite3_stmt* stmt;
  int rc = sqlite3_prepare_v2(db_, sql.c_str(), -1, &stmt, nullptr);
  if (rc != SQLITE_OK)
  {
    std::string error_msg =
        "准备区域信息查询语句失败: " + std::string(sqlite3_errmsg(db_));
    LOG(ERROR) << error_msg;
    throw std::runtime_error(error_msg);
  }

  while (sqlite3_step(stmt) == SQLITE_ROW)
  {
    AreaInfo area;
    area.id = sqlite3_column_int(stmt, 0);
    area.area_mode = sqlite3_column_int(stmt, 1);
    std::string area_points_id =
        reinterpret_cast<const char*>(sqlite3_column_text(stmt, 2));
    area.area_points = parse_area_points(area_points_id);
    area.area_param = sqlite3_column_int(stmt, 3);
    area.floor = sqlite3_column_int(stmt, 4);
    results.push_back(area);
  }

  sqlite3_finalize(stmt);
  VLOG(1) << "查询到 " << results.size() << " 个区域信息";
  return results;
}

std::vector<SpecialSite> DatabaseManager::query_special_sites(int id, int floor,
                                                              int site_nature)
{
  std::vector<SpecialSite> results;

  std::string sql =
      "SELECT id, name, site_nature, site_point_name, site_theta, floor FROM "
      "special_site_tb WHERE 1=1";
  if (id != 0)
  {
    sql += " AND id = " + std::to_string(id);
  }
  if (floor != 0)
  {
    sql += " AND floor = " + std::to_string(floor);
  }
  if (site_nature != -1)
  {
    sql += " AND site_nature = " + std::to_string(site_nature);
  }

  sqlite3_stmt* stmt;
  int rc = sqlite3_prepare_v2(db_, sql.c_str(), -1, &stmt, nullptr);
  if (rc != SQLITE_OK)
  {
    std::string error_msg =
        "准备站点查询语句失败: " + std::string(sqlite3_errmsg(db_));
    LOG(ERROR) << error_msg;
    throw std::runtime_error(error_msg);
  }

  while (sqlite3_step(stmt) == SQLITE_ROW)
  {
    SpecialSite site;
    site.id = sqlite3_column_int(stmt, 0);
    site.name = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 1));
    site.site_nature = sqlite3_column_int(stmt, 2);
    site.site_point_name =
        reinterpret_cast<const char*>(sqlite3_column_text(stmt, 3));
    site.site_theta = sqlite3_column_double(stmt, 4);
    site.floor = sqlite3_column_int(stmt, 5);
    results.push_back(site);
  }

  sqlite3_finalize(stmt);
  VLOG(1) << "查询到 " << results.size() << " 个特殊站点";
  return results;
}

std::vector<TaskInfo> DatabaseManager::query_task_info(int id, int floor)
{
  std::vector<TaskInfo> results;

  // 使用JOIN查询site_info和path_point表
  std::string sql = R"(
    SELECT
      si.id, si.name, si.point_id, si.site_theta, si.floor, si.operation,
      si.dir, si.param1, si.param2, si.param3, si.site_point_x, si.site_point_y,
      si.site_point_valid, si.other, si.layer,
      pp.name as point_name, pp.x as point_x, pp.y as point_y,
      pp.floor as point_floor, pp.property as point_property, pp.is_rotated_in_place
    FROM site_info si
    LEFT JOIN path_point pp ON si.point_id = pp.id
    WHERE 1=1
  )";

  if (id != 0)
  {
    sql += " AND si.id = " + std::to_string(id);
  }
  if (floor != 0)
  {
    sql += " AND si.floor = " + std::to_string(floor);
  }

  sqlite3_stmt* stmt;
  int rc = sqlite3_prepare_v2(db_, sql.c_str(), -1, &stmt, nullptr);
  if (rc != SQLITE_OK)
  {
    std::string error_msg =
        "准备任务信息查询语句失败: " + std::string(sqlite3_errmsg(db_));
    LOG(ERROR) << error_msg;
    throw std::runtime_error(error_msg);
  }

  while (sqlite3_step(stmt) == SQLITE_ROW)
  {
    TaskInfo task;
    // 任务信息（来自site_info表）
    task.id = sqlite3_column_int(stmt, 0);
    task.name = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 1));
    task.point_id = sqlite3_column_int(stmt, 2);
    task.site_theta = sqlite3_column_double(stmt, 3);
    task.floor = sqlite3_column_int(stmt, 4);
    task.operation =
        reinterpret_cast<const char*>(sqlite3_column_text(stmt, 5));
    task.dir = sqlite3_column_int(stmt, 6) != 0;
    task.param1 = sqlite3_column_double(stmt, 7);
    task.param2 = sqlite3_column_double(stmt, 8);
    task.param3 = sqlite3_column_double(stmt, 9);
    task.site_point_x = sqlite3_column_double(stmt, 10);
    task.site_point_y = sqlite3_column_double(stmt, 11);
    task.site_point_valid = sqlite3_column_int(stmt, 12) != 0;
    task.other = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 13));
    task.layer = sqlite3_column_int(stmt, 14);

    // 路径点信息（来自path_point表）
    if (sqlite3_column_type(stmt, 15) != SQLITE_NULL)
    {
      task.point_name =
          reinterpret_cast<const char*>(sqlite3_column_text(stmt, 15));
      task.point_x = sqlite3_column_double(stmt, 16);
      task.point_y = sqlite3_column_double(stmt, 17);
      task.point_floor = sqlite3_column_int(stmt, 18);
      task.point_property =
          static_cast<PointProperty>(sqlite3_column_int(stmt, 19));
      task.point_is_rotated_in_place = sqlite3_column_int(stmt, 20) != 0;
    }
    else
    {
      // 如果没有关联的路径点，设置默认值
      task.point_name = "";
      task.point_x = 0.0;
      task.point_y = 0.0;
      task.point_floor = 0;
      task.point_property = PointProperty::PRI_POINT;
      task.point_is_rotated_in_place = false;
    }

    results.push_back(task);
  }

  sqlite3_finalize(stmt);
  VLOG(1) << "查询到 " << results.size() << " 个任务信息";
  return results;
}

std::vector<LayerSwitchPoint> DatabaseManager::query_layer_switch_points(
    int id, int floor, int point_id)
{
  std::vector<LayerSwitchPoint> results;

  std::string sql =
      "SELECT id, name, nature, point_id, floor, in_theta, out_theta, "
      "is_active FROM layer_switch_point WHERE 1=1";

  if (id != 0)
  {
    sql += " AND id = " + std::to_string(id);
  }
  if (floor != 0)
  {
    sql += " AND floor = " + std::to_string(floor);
  }
  if (point_id != 0)
  {
    sql += " AND point_id = " + std::to_string(point_id);
  }

  sqlite3_stmt* stmt;
  int rc = sqlite3_prepare_v2(db_, sql.c_str(), -1, &stmt, nullptr);
  if (rc != SQLITE_OK)
  {
    std::string error_msg =
        "准备楼层切换点查询语句失败: " + std::string(sqlite3_errmsg(db_));
    LOG(ERROR) << error_msg;
    throw std::runtime_error(error_msg);
  }

  while (sqlite3_step(stmt) == SQLITE_ROW)
  {
    LayerSwitchPoint point;
    point.id = sqlite3_column_int(stmt, 0);
    point.name = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 1));
    point.nature = sqlite3_column_int(stmt, 2);
    point.point_id = sqlite3_column_int(stmt, 3);
    point.floor = sqlite3_column_int(stmt, 4);
    point.in_theta = sqlite3_column_double(stmt, 5);
    point.out_theta = sqlite3_column_double(stmt, 6);
    point.is_active = sqlite3_column_int(stmt, 7) != 0;
    results.push_back(point);
  }

  sqlite3_finalize(stmt);
  VLOG(1) << "查询到 " << results.size() << " 个楼层切换点";
  return results;
}

std::vector<Point2D> DatabaseManager::parse_area_points(
    const std::string& area_points_id)
{
  std::vector<Point2D> points;

  // 解析点ID字符串，"9-10-11-12"
  std::istringstream iss(area_points_id);
  std::string item;
  std::vector<int> point_ids;

  while (std::getline(iss, item, '-'))
  {
    try
    {
      int point_id = std::stoi(item);
      point_ids.push_back(point_id);
    }
    catch (const std::exception& e)
    {
      LOG(WARNING) << "解析点ID失败: " << item;
      continue;
    }
  }

  // 查询每个点的坐标
  for (int point_id : point_ids)
  {
    std::string sql = "SELECT x, y FROM area_point_tb WHERE id = ?";
    sqlite3_stmt* stmt;
    int rc = sqlite3_prepare_v2(db_, sql.c_str(), -1, &stmt, nullptr);
    if (rc != SQLITE_OK)
    {
      std::string error_msg =
          "准备区域点查询语句失败: " + std::string(sqlite3_errmsg(db_));
      LOG(ERROR) << error_msg;
      throw std::runtime_error(error_msg);
    }

    sqlite3_bind_int(stmt, 1, point_id);

    while (sqlite3_step(stmt) == SQLITE_ROW)
    {
      Point2D point;
      point.x = sqlite3_column_double(stmt, 0);
      point.y = sqlite3_column_double(stmt, 1);
      points.push_back(point);
    }

    sqlite3_finalize(stmt);
  }

  VLOG(1) << "解析到 " << points.size() << " 个区域点";
  return points;
}

std::vector<LayerSwitchConnection>
DatabaseManager::query_layer_switch_connections(int from_switch_path_point_id,
                                                int to_switch_path_point_id)
{
  std::vector<LayerSwitchConnection> results;

  // 需要通过path_point_id查询，所以要联查layer_switch_point表
  std::string sql = R"(
    SELECT
      lsc.id, fp.point_id as from_point_id, tp.point_id as to_point_id, lsc.is_active
    FROM layer_switch_connection lsc
    JOIN layer_switch_point fp ON lsc.from_switch_id = fp.id
    JOIN layer_switch_point tp ON lsc.to_switch_id = tp.id
    WHERE 1=1
  )";

  if (from_switch_path_point_id != 0)
  {
    sql += " AND fp.point_id = " + std::to_string(from_switch_path_point_id);
  }
  if (to_switch_path_point_id != 0)
  {
    sql += " AND tp.point_id = " + std::to_string(to_switch_path_point_id);
  }

  sqlite3_stmt* stmt;
  int rc = sqlite3_prepare_v2(db_, sql.c_str(), -1, &stmt, nullptr);
  if (rc != SQLITE_OK)
  {
    std::string error_msg =
        "准备楼层切换连接查询语句失败: " + std::string(sqlite3_errmsg(db_));
    LOG(ERROR) << error_msg;
    throw std::runtime_error(error_msg);
  }

  while (sqlite3_step(stmt) == SQLITE_ROW)
  {
    LayerSwitchConnection connection;
    connection.id = sqlite3_column_int(stmt, 0);
    connection.from_switch_path_point_id = sqlite3_column_int(stmt, 1);
    connection.to_switch_path_point_id = sqlite3_column_int(stmt, 2);
    connection.is_active = sqlite3_column_int(stmt, 3) != 0;
    results.push_back(connection);
  }

  sqlite3_finalize(stmt);
  VLOG(1) << "查询到 " << results.size() << " 个楼层切换连接";
  return results;
}

std::vector<LayerSwitchConnectionInfo>
DatabaseManager::query_layer_switch_connection_info(int path_point_id,
                                                    int floor)
{
  std::vector<LayerSwitchConnectionInfo> results;
  std::map<int, LayerSwitchConnectionInfo> connection_map;

  // 查询连接关系的SQL，包含起始点和目标点的详细信息
  std::string sql = R"(
    SELECT
      fp.name as from_name, fp.point_id as from_point_id,
      pp_from.name as from_point_name, fp.floor as from_floor, fp.out_theta as from_out_theta,
      tp.point_id as to_point_id, pp_to.name as to_point_name,
      tp.in_theta as to_in_theta, tp.floor as to_floor
    FROM layer_switch_connection lsc
    JOIN layer_switch_point fp ON lsc.from_switch_id = fp.id
    JOIN layer_switch_point tp ON lsc.to_switch_id = tp.id
    JOIN path_point pp_from ON fp.point_id = pp_from.id
    JOIN path_point pp_to ON tp.point_id = pp_to.id
    WHERE lsc.is_active = 1
  )";

  if (path_point_id != 0)
  {
    sql += " AND fp.point_id = " + std::to_string(path_point_id);
  }
  if (floor != 0)
  {
    sql += " AND fp.floor = " + std::to_string(floor);
  }

  sql += " ORDER BY fp.point_id, tp.floor";

  sqlite3_stmt* stmt;
  int rc = sqlite3_prepare_v2(db_, sql.c_str(), -1, &stmt, nullptr);
  if (rc != SQLITE_OK)
  {
    std::string error_msg =
        "准备楼层切换连接信息查询语句失败: " + std::string(sqlite3_errmsg(db_));
    LOG(ERROR) << error_msg;
    throw std::runtime_error(error_msg);
  }

  while (sqlite3_step(stmt) == SQLITE_ROW)
  {
    // 获取起始点信息
    std::string from_name =
        reinterpret_cast<const char*>(sqlite3_column_text(stmt, 0));
    int from_point_id = sqlite3_column_int(stmt, 1);
    std::string from_point_name =
        reinterpret_cast<const char*>(sqlite3_column_text(stmt, 2));
    int from_floor = sqlite3_column_int(stmt, 3);
    double from_out_theta = sqlite3_column_double(stmt, 4);

    // 获取目标点信息
    int to_point_id = sqlite3_column_int(stmt, 5);
    std::string to_point_name =
        reinterpret_cast<const char*>(sqlite3_column_text(stmt, 6));
    double to_in_theta = sqlite3_column_double(stmt, 7);
    int to_floor = sqlite3_column_int(stmt, 8);

    // 查找或创建连接信息
    if (connection_map.find(from_point_id) == connection_map.end())
    {
      LayerSwitchConnectionInfo info;
      info.source_name = from_name;
      info.source_point_id = from_point_id;
      info.source_point_number = extract_point_number(from_point_name);
      info.source_floor = from_floor;
      info.source_out_theta = from_out_theta;
      connection_map[from_point_id] = info;
    }

    // 添加可达点
    ReachablePoint reachable;
    reachable.point_id = to_point_id;
    reachable.point_number = extract_point_number(to_point_name);
    reachable.in_theta = to_in_theta;
    reachable.floor = to_floor;
    connection_map[from_point_id].reachable_points.push_back(reachable);
  }

  sqlite3_finalize(stmt);

  // 转换为结果向量
  for (auto& pair : connection_map)
  {
    results.push_back(pair.second);
  }

  VLOG(1) << "查询到 " << results.size() << " 个点的连接信息";
  return results;
}

int DatabaseManager::extract_point_number(const std::string& point_name)
{
  // 查找最后一个 '-' 的位置
  size_t dash_pos = point_name.find_last_of('-');
  if (dash_pos != std::string::npos && dash_pos < point_name.length() - 1)
  {
    // 提取 '-' 后面的数字部分
    std::string number_str = point_name.substr(dash_pos + 1);
    try
    {
      return std::stoi(number_str);
    }
    catch (const std::exception& e)
    {
      LOG(WARNING) << "无法从点名 '" << point_name
                   << "' 中提取数字: " << e.what();
      return 0;
    }
  }

  LOG(WARNING) << "点名 '" << point_name << "' 格式不正确，无法提取数字";
  return 0;
}

}  // namespace database_service
