#include "database_service/database_service_node.hpp"
#include <database_interfaces/msg/path_point.hpp>
#include <database_interfaces/msg/path_route.hpp>
#include <database_interfaces/msg/site_info.hpp>
#include <database_interfaces/msg/special_site.hpp>
#include <database_interfaces/msg/task_info.hpp>
#include <database_interfaces/msg/area_info.hpp>
#include <database_interfaces/msg/point2_d.hpp>
#include <glog/logging.h>

namespace database_service
{

DatabaseServiceNode::DatabaseServiceNode() : Node("database_service_node")
{
  LOG(INFO) << "=== 数据库服务节点启动 ===";

  // 初始化数据库
  std::string db_path = "/home/<USER>/DB_Maps/mapreflectors.db";
  db_manager_ = std::make_shared<DatabaseManager>(db_path);

  if (!db_manager_->initialize())
  {
    LOG(ERROR) << "数据库初始化失败";
    return;
  }

  // 创建路径点查询服务
  path_point_service_ =
      this->create_service<database_interfaces::srv::QueryPathPoint>(
          "query_path_point",
          std::bind(&DatabaseServiceNode::handle_query_path_point, this,
                    std::placeholders::_1, std::placeholders::_2));

  // 创建路径路线查询服务
  path_route_service_ =
      this->create_service<database_interfaces::srv::QueryPathRoute>(
          "query_path_route",
          std::bind(&DatabaseServiceNode::handle_query_path_route, this,
                    std::placeholders::_1, std::placeholders::_2));

  // 创建站点信息查询服务
  site_info_service_ =
      this->create_service<database_interfaces::srv::QuerySiteInfo>(
          "query_site_info",
          std::bind(&DatabaseServiceNode::handle_query_site_info, this,
                    std::placeholders::_1, std::placeholders::_2));

  // 创建区域信息查询服务
  area_info_service_ =
      this->create_service<database_interfaces::srv::QueryAreaInfo>(
          "query_area_info",
          std::bind(&DatabaseServiceNode::handle_query_area_info, this,
                    std::placeholders::_1, std::placeholders::_2));

  // 创建特殊站点查询服务
  special_site_service_ =
      this->create_service<database_interfaces::srv::QuerySpecialSite>(
          "query_special_site",
          std::bind(&DatabaseServiceNode::handle_query_special_site, this,
                    std::placeholders::_1, std::placeholders::_2));

  // 创建任务信息查询服务
  task_info_service_ =
      this->create_service<database_interfaces::srv::QueryTaskInfo>(
          "query_task_info",
          std::bind(&DatabaseServiceNode::handle_query_task_info, this,
                    std::placeholders::_1, std::placeholders::_2));

  LOG(INFO) << "所有数据库服务已启动";
}

void DatabaseServiceNode::handle_query_path_point(
    const std::shared_ptr<database_interfaces::srv::QueryPathPoint::Request>
        request,
    std::shared_ptr<database_interfaces::srv::QueryPathPoint::Response>
        response)
{
  LOG(INFO) << "[路径点查询] 收到请求: id=" << request->id
            << ", floor=" << request->floor;

  try
  {
    auto points = db_manager_->query_path_points(request->id, request->floor);

    // 转换为ROS消息
    for (const auto& point : points)
    {
      database_interfaces::msg::PathPoint msg;
      msg.id = point.id;
      msg.name = point.name;
      msg.x = point.x;
      msg.y = point.y;
      msg.point_property.value = static_cast<uint8_t>(point.point_property);
      msg.is_rotated_in_place = point.is_rotated_in_place;
      msg.floor = point.floor;
      response->data.push_back(msg);
    }

    response->success = true;
    response->message =
        "查询成功，找到 " + std::to_string(points.size()) + " 个路径点";

    LOG(INFO) << "[路径点查询] 查询成功，返回 " << points.size() << " 个路径点";
  }
  catch (const std::exception& e)
  {
    response->success = false;
    response->message = "查询失败: " + std::string(e.what());
    LOG(ERROR) << "[路径点查询] 查询失败: " << e.what();
  }
}

void DatabaseServiceNode::handle_query_path_route(
    const std::shared_ptr<database_interfaces::srv::QueryPathRoute::Request>
        request,
    std::shared_ptr<database_interfaces::srv::QueryPathRoute::Response>
        response)
{
  LOG(INFO) << "[路径路线查询] 收到请求: floor=" << request->floor
            << ", id=" << request->id;

  try
  {
    auto routes = db_manager_->query_path_routes(request->id, request->floor);

    // 转换为ROS消息
    for (const auto& route : routes)
    {
      database_interfaces::msg::PathRoute msg;
      msg.id = route.id;
      msg.name = route.name;
      msg.type.value = static_cast<uint8_t>(route.type);
      msg.start = route.start;
      msg.end = route.end;
      msg.c1_x = route.c1_x;
      msg.c1_y = route.c1_y;
      msg.c2_x = route.c2_x;
      msg.c2_y = route.c2_y;
      msg.length = route.length;
      msg.route_property.value = static_cast<uint8_t>(route.route_property);
      msg.floor = route.floor;
      msg.width = route.width;
      msg.movement = route.movement;
      msg.max_speed = route.max_speed;
      response->data.push_back(msg);
    }

    response->success = true;
    response->message =
        "查询成功，找到 " + std::to_string(routes.size()) + " 个路径路线";

    LOG(INFO) << "[路径路线查询] 查询成功，返回 " << routes.size()
              << " 个路径路线";
  }
  catch (const std::exception& e)
  {
    response->success = false;
    response->message = "查询失败: " + std::string(e.what());
    LOG(ERROR) << "[路径路线查询] 查询失败: " << e.what();
  }
}

void DatabaseServiceNode::handle_query_site_info(
    const std::shared_ptr<database_interfaces::srv::QuerySiteInfo::Request>
        request,
    std::shared_ptr<database_interfaces::srv::QuerySiteInfo::Response> response)
{
  LOG(INFO) << "[站点信息查询] 收到请求: id=" << request->id
            << ", floor=" << request->floor;

  try
  {
    auto sites = db_manager_->query_site_info(request->id, request->floor);

    // 转换为ROS消息
    for (const auto& site : sites)
    {
      database_interfaces::msg::SiteInfo msg;
      msg.id = site.id;
      msg.name = site.name;
      msg.point_id = site.point_id;
      msg.site_theta = site.site_theta;
      msg.floor = site.floor;
      msg.operation = site.operation;
      msg.dir = site.dir;
      msg.param1 = site.param1;
      msg.param2 = site.param2;
      msg.param3 = site.param3;
      msg.site_point_x = site.site_point_x;
      msg.site_point_y = site.site_point_y;
      msg.site_point_valid = site.site_point_valid;
      msg.other = site.other;
      msg.layer = site.layer;
      response->data.push_back(msg);
    }

    response->success = true;
    response->message =
        "查询成功，找到 " + std::to_string(sites.size()) + " 个站点";

    LOG(INFO) << "[站点信息查询] 查询成功，返回 " << sites.size() << " 个站点";
  }
  catch (const std::exception& e)
  {
    response->success = false;
    response->message = "查询失败: " + std::string(e.what());
    LOG(ERROR) << "[站点信息查询] 查询失败: " << e.what();
  }
}

void DatabaseServiceNode::handle_query_area_info(
    const std::shared_ptr<database_interfaces::srv::QueryAreaInfo::Request>
        request,
    std::shared_ptr<database_interfaces::srv::QueryAreaInfo::Response> response)
{
  LOG(INFO) << "[区域信息查询] 收到请求: id=" << request->id
            << ", floor=" << request->floor;

  try
  {
    auto areas = db_manager_->query_area_info(request->id, request->floor);

    // 转换为ROS消息
    for (const auto& area : areas)
    {
      database_interfaces::msg::AreaInfo msg;
      msg.id = area.id;
      msg.area_mode = area.area_mode;
      msg.area_param = area.area_param;
      msg.floor = area.floor;

      // 转换区域点
      for (const auto& point : area.area_points)
      {
        database_interfaces::msg::Point2D point_msg;
        point_msg.x = point.x;
        point_msg.y = point.y;
        msg.area_points.push_back(point_msg);
      }

      response->data.push_back(msg);
    }

    response->success = true;
    response->message =
        "查询成功，找到 " + std::to_string(areas.size()) + " 个区域";

    LOG(INFO) << "[区域信息查询] 查询成功，返回 " << areas.size() << " 个区域";
  }
  catch (const std::exception& e)
  {
    response->success = false;
    response->message = "查询失败: " + std::string(e.what());
    LOG(ERROR) << "[区域信息查询] 查询失败: " << e.what();
  }
}

void DatabaseServiceNode::handle_query_special_site(
    const std::shared_ptr<database_interfaces::srv::QuerySpecialSite::Request>
        request,
    std::shared_ptr<database_interfaces::srv::QuerySpecialSite::Response>
        response)
{
  LOG(INFO) << "[特殊站点查询] 收到请求: id=" << request->id
            << ", floor=" << request->floor
            << ", site_nature=" << request->site_nature;

  try
  {
    auto sites = db_manager_->query_special_sites(request->id, request->floor,
                                                  request->site_nature);

    // 转换为ROS消息
    for (const auto& site : sites)
    {
      database_interfaces::msg::SpecialSite msg;
      msg.id = site.id;
      msg.name = site.name;
      msg.site_nature = site.site_nature;
      msg.site_point_name = site.site_point_name;
      msg.site_theta = site.site_theta;
      msg.floor = site.floor;
      response->data.push_back(msg);
    }

    response->success = true;
    response->message =
        "查询成功，找到 " + std::to_string(sites.size()) + " 个特殊站点";

    LOG(INFO) << "[特殊站点查询] 查询成功，返回 " << sites.size()
              << " 个特殊站点";
  }
  catch (const std::exception& e)
  {
    response->success = false;
    response->message = "查询失败: " + std::string(e.what());
    LOG(ERROR) << "[特殊站点查询] 查询失败: " << e.what();
  }
}

void DatabaseServiceNode::handle_query_task_info(
    const std::shared_ptr<database_interfaces::srv::QueryTaskInfo::Request>
        request,
    std::shared_ptr<database_interfaces::srv::QueryTaskInfo::Response> response)
{
  LOG(INFO) << "[任务信息查询] 收到请求: id=" << request->id
            << ", floor=" << request->floor;

  try
  {
    auto tasks = db_manager_->query_task_info(request->id, request->floor);

    // 转换为ROS消息
    for (const auto& task : tasks)
    {
      database_interfaces::msg::TaskInfo msg;
      // 任务信息
      msg.id = task.id;
      msg.name = task.name;
      msg.point_id = task.point_id;
      msg.site_theta = task.site_theta;
      msg.floor = task.floor;
      msg.operation = task.operation;
      msg.dir = task.dir;
      msg.param1 = task.param1;
      msg.param2 = task.param2;
      msg.param3 = task.param3;
      msg.site_point_x = task.site_point_x;
      msg.site_point_y = task.site_point_y;
      msg.site_point_valid = task.site_point_valid;
      msg.other = task.other;
      msg.layer = task.layer;

      // 关联的路径点信息
      msg.point_name = task.point_name;
      msg.point_x = task.point_x;
      msg.point_y = task.point_y;
      msg.point_floor = task.point_floor;
      msg.point_property.value = static_cast<uint8_t>(task.point_property);
      msg.point_is_rotated_in_place = task.point_is_rotated_in_place;

      response->data.push_back(msg);
    }

    response->success = true;
    response->message =
        "查询成功，找到 " + std::to_string(tasks.size()) + " 个任务信息";

    LOG(INFO) << "[任务信息查询] 查询成功，返回 " << tasks.size()
              << " 个任务信息";
  }
  catch (const std::exception& e)
  {
    response->success = false;
    response->message = "查询失败: " + std::string(e.what());
    LOG(ERROR) << "[任务信息查询] 查询失败: " << e.what();
  }
}

}  // namespace database_service
