#include <glog/logging.h>
#include <rclcpp/rclcpp.hpp>
#include "database_service/database_service_node.hpp"

int main(int argc, char* argv[])
{
  // 初始化glog
  google::InitGoogleLogging(argv[0]);
  FLAGS_logtostderr = 1;
  FLAGS_colorlogtostderr = 1;
  // 启用VLOG(1)
  FLAGS_v = 1;

  rclcpp::init(argc, argv);

  try
  {
    auto node = std::make_shared<database_service::DatabaseServiceNode>();

    LOG(INFO) << "数据库服务开始运行...";

    rclcpp::spin(node);
  }
  catch (const std::exception& e)
  {
    LOG(ERROR) << "服务运行异常: " << e.what();
    return 1;
  }

  rclcpp::shutdown();
  LOG(INFO) << "数据库服务已关闭";

  return 0;
}
