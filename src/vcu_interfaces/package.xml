<?xml version="1.0"?>
<package format="3">
  <name>vcu_interfaces</name>
  <version>0.0.0</version>
  <description>The vcu_interfaces package</description>
  
  <maintainer email="<EMAIL>">hit</maintainer>   
  <license>TODO: License declaration</license>

  <license>TODO</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <build_depend>rosidl_default_generators</build_depend>
  <exec_depend>rosidl_default_runtime</exec_depend>
  <exec_depend>std_msgs</exec_depend>
  <exec_depend>hit_nav_interfaces</exec_depend>
  <member_of_group>rosidl_interface_packages</member_of_group>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package> 