cmake_minimum_required(VERSION 3.5)
project(vcu_interfaces)

find_package(ament_cmake REQUIRED)
find_package(rosidl_default_generators REQUIRED)
find_package(std_msgs REQUIRED)
find_package(hit_nav_interfaces REQUIRED)

rosidl_generate_interfaces(${PROJECT_NAME}
  
  "msg/TaskStruct.msg"
  "msg/LocalInfo.msg"
  "msg/PilingStatus.msg"
  "msg/BatteryInfo.msg"
  "msg/OdomInfo.msg"
  "msg/TaskInfo.msg"
  "msg/ErrorArray.msg"
  
  "srv/PilingManageCheck.srv"
  "srv/PilingTaskSet.srv"
  "srv/CommandPub.srv"
  "srv/ReLocate.srv"
  "srv/DBLoad.srv"
  "srv/SwtichFloor.srv"
  "srv/TaskLists.srv"
  "srv/TaskQuery.srv"
  DEPENDENCIES std_msgs hit_nav_interfaces
)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  # the following line skips the linter which checks for copyrights
  # uncomment the line when a copyright and license is not present in all source files
  #set(ament_cmake_copyright_FOUND TRUE)
  # the following line skips cpplint (only works in a git repo)
  # uncomment the line when this package is not in a git repo
  #set(ament_cmake_cpplint_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
endif()

ament_package() 