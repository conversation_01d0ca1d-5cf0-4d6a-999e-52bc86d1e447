cmake_minimum_required(VERSION 3.8)
project(database_interfaces)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(std_msgs REQUIRED)
find_package(rosidl_default_generators REQUIRED)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  # the following line skips the linter which checks for copyrights
  # comment the line when a copyright and license is added to all source files
  set(ament_cmake_copyright_FOUND TRUE)
  # the following line skips cpplint (only works in a git repo)
  # comment the line when this package is in a git repo and when
  # a copyright and license is added to all source files
  set(ament_cmake_cpplint_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
endif()

rosidl_generate_interfaces(${PROJECT_NAME}
  "msg/PathPoint.msg"
  "msg/PathRoute.msg"
  "msg/SiteInfo.msg"
  "msg/SpecialSite.msg"
  "msg/TaskInfo.msg"
  "msg/Point2D.msg"
  "msg/AreaInfo.msg"
  "msg/PointProperty.msg"
  "msg/RouteType.msg"
  "msg/RouteProperty.msg"
  "msg/LayerSwitchPoint.msg"
  "msg/LayerSwitchConnection.msg"
  "msg/ReachablePoint.msg"
  "msg/LayerSwitchConnectionInfo.msg"
  "srv/QueryPathPoint.srv"
  "srv/QueryPathRoute.srv"
  "srv/QuerySiteInfo.srv"
  "srv/QuerySpecialSite.srv"
  "srv/QueryTaskInfo.srv"
  "srv/QueryAreaInfo.srv"
  "srv/QueryLayerSwitchPoint.srv"
  "srv/QueryLayerSwitchConnection.srv"
  "srv/QueryLayerSwitchConnectionInfo.srv"
  DEPENDENCIES std_msgs
)

ament_package()
