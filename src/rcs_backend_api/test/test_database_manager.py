"""
Tests for Database Manager

This module contains unit tests for the DatabaseManager class.
"""

import unittest
import tempfile
import os
import sqlite3
from unittest.mock import patch, MagicMock

from rcs_backend_api.database import DatabaseManager
from rcs_backend_api.models import PathPoint, PointProperty


class TestDatabaseManager(unittest.TestCase):
    """数据库管理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时数据库文件
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.db_path = self.temp_db.name
        
        # 创建数据库管理器实例
        self.db_manager = DatabaseManager(self.db_path)
        
    def tearDown(self):
        """测试后清理"""
        # 删除临时数据库文件
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)
    
    def test_initialize_success(self):
        """测试数据库初始化成功"""
        result = self.db_manager.initialize()
        self.assertTrue(result)
        
        # 验证数据库文件存在
        self.assertTrue(os.path.exists(self.db_path))
        
        # 验证表已创建
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            expected_tables = [
                'path_point', 'path_route', 'site_info', 'area_info',
                'area_point_tb', 'special_site_tb', 'layer_switch_point',
                'layer_switch_connection'
            ]
            
            for table in expected_tables:
                self.assertIn(table, tables)
    
    def test_initialize_invalid_path(self):
        """测试无效路径的数据库初始化"""
        invalid_db_manager = DatabaseManager("/invalid/path/test.db")
        result = invalid_db_manager.initialize()
        self.assertFalse(result)
    
    def test_query_path_points_empty(self):
        """测试查询空的路径点表"""
        self.db_manager.initialize()
        
        result = self.db_manager.query_path_points()
        self.assertEqual(len(result), 0)
        self.assertIsInstance(result, list)
    
    def test_query_path_points_with_data(self):
        """测试查询包含数据的路径点表"""
        self.db_manager.initialize()
        
        # 插入测试数据
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO path_point (id, name, x, y, point_property, is_rotated_in_place, floor)
                VALUES (1, 'Point-1', 10.0, 20.0, 0, 1, 1)
            """)
            cursor.execute("""
                INSERT INTO path_point (id, name, x, y, point_property, is_rotated_in_place, floor)
                VALUES (2, 'Point-2', 30.0, 40.0, 1, 0, 1)
            """)
            conn.commit()
        
        # 查询所有路径点
        result = self.db_manager.query_path_points()
        self.assertEqual(len(result), 2)
        
        # 验证第一个路径点
        point1 = result[0]
        self.assertIsInstance(point1, PathPoint)
        self.assertEqual(point1.id, 1)
        self.assertEqual(point1.name, 'Point-1')
        self.assertEqual(point1.x, 10.0)
        self.assertEqual(point1.y, 20.0)
        self.assertEqual(point1.point_property, PointProperty.PRI_POINT)
        self.assertTrue(point1.is_rotated_in_place)
        self.assertEqual(point1.floor, 1)
        
        # 按ID查询
        result_by_id = self.db_manager.query_path_points(id=1)
        self.assertEqual(len(result_by_id), 1)
        self.assertEqual(result_by_id[0].id, 1)
        
        # 按楼层查询
        result_by_floor = self.db_manager.query_path_points(floor=1)
        self.assertEqual(len(result_by_floor), 2)
    
    def test_query_path_routes_empty(self):
        """测试查询空的路径路线表"""
        self.db_manager.initialize()
        
        result = self.db_manager.query_path_routes()
        self.assertEqual(len(result), 0)
        self.assertIsInstance(result, list)
    
    def test_query_site_info_empty(self):
        """测试查询空的站点信息表"""
        self.db_manager.initialize()
        
        result = self.db_manager.query_site_info()
        self.assertEqual(len(result), 0)
        self.assertIsInstance(result, list)
    
    def test_query_area_info_empty(self):
        """测试查询空的区域信息表"""
        self.db_manager.initialize()
        
        result = self.db_manager.query_area_info()
        self.assertEqual(len(result), 0)
        self.assertIsInstance(result, list)
    
    @patch('rcs_backend_api.database.manager.logging')
    def test_query_error_handling(self, mock_logging):
        """测试查询错误处理"""
        # 不初始化数据库，直接查询应该抛出异常
        with self.assertRaises(Exception):
            self.db_manager.query_path_points()


if __name__ == '__main__':
    unittest.main()
