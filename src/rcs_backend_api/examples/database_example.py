#!/usr/bin/env python3
"""
Database Example for RCS Backend API

This example demonstrates how to use the DatabaseManager class
to query the RCS database.
"""

import sys
import os
import logging

# Add the package to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from rcs_backend_api.database import DatabaseManager
from rcs_backend_api.models import PointProperty, RouteType, RouteProperty


def main():
    """Main example function"""
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    # Database path
    db_path = "/home/<USER>/DB_Maps/mapreflectors.db"
    
    # Check if database file exists
    if not os.path.exists(db_path):
        logger.error(f"数据库文件不存在: {db_path}")
        logger.info("请确保数据库文件路径正确")
        return 1
    
    try:
        # Create database manager
        logger.info("创建数据库管理器...")
        db_manager = DatabaseManager(db_path)
        
        # Initialize database
        logger.info("初始化数据库连接...")
        if not db_manager.initialize():
            logger.error("数据库初始化失败")
            return 1
        
        logger.info("数据库连接成功！")
        
        # Query path points
        logger.info("\n=== 查询路径点 ===")
        path_points = db_manager.query_path_points()
        logger.info(f"总共找到 {len(path_points)} 个路径点")
        
        if path_points:
            # Show first few path points
            for i, point in enumerate(path_points[:5]):
                logger.info(f"路径点 {i+1}: ID={point.id}, 名称={point.name}, "
                          f"坐标=({point.x:.2f}, {point.y:.2f}), "
                          f"楼层={point.floor}, 属性={point.point_property}")
            
            if len(path_points) > 5:
                logger.info(f"... 还有 {len(path_points) - 5} 个路径点")
        
        # Query path routes
        logger.info("\n=== 查询路径路线 ===")
        path_routes = db_manager.query_path_routes()
        logger.info(f"总共找到 {len(path_routes)} 条路径路线")
        
        if path_routes:
            # Show first few path routes
            for i, route in enumerate(path_routes[:3]):
                logger.info(f"路径路线 {i+1}: ID={route.id}, 名称={route.name}, "
                          f"类型={route.type}, 起点={route.start}, 终点={route.end}, "
                          f"楼层={route.floor}")
            
            if len(path_routes) > 3:
                logger.info(f"... 还有 {len(path_routes) - 3} 条路径路线")
        
        # Query site info
        logger.info("\n=== 查询站点信息 ===")
        site_infos = db_manager.query_site_info()
        logger.info(f"总共找到 {len(site_infos)} 个站点信息")
        
        if site_infos:
            # Show first few site infos
            for i, site in enumerate(site_infos[:3]):
                logger.info(f"站点信息 {i+1}: ID={site.id}, 名称={site.name}, "
                          f"点ID={site.point_id}, 楼层={site.floor}, "
                          f"操作={site.operation}")
            
            if len(site_infos) > 3:
                logger.info(f"... 还有 {len(site_infos) - 3} 个站点信息")
        
        # Query area info
        logger.info("\n=== 查询区域信息 ===")
        area_infos = db_manager.query_area_info()
        logger.info(f"总共找到 {len(area_infos)} 个区域信息")
        
        if area_infos:
            # Show first few area infos
            for i, area in enumerate(area_infos[:3]):
                logger.info(f"区域信息 {i+1}: ID={area.id}, 模式={area.area_mode}, "
                          f"楼层={area.floor}, 点数量={len(area.area_points)}")
            
            if len(area_infos) > 3:
                logger.info(f"... 还有 {len(area_infos) - 3} 个区域信息")
        
        # Query special sites
        logger.info("\n=== 查询特殊站点 ===")
        special_sites = db_manager.query_special_sites()
        logger.info(f"总共找到 {len(special_sites)} 个特殊站点")
        
        if special_sites:
            # Show first few special sites
            for i, site in enumerate(special_sites[:3]):
                logger.info(f"特殊站点 {i+1}: ID={site.id}, 名称={site.name}, "
                          f"性质={site.site_nature}, 楼层={site.floor}")
            
            if len(special_sites) > 3:
                logger.info(f"... 还有 {len(special_sites) - 3} 个特殊站点")
        
        # Query task info
        logger.info("\n=== 查询任务信息 ===")
        task_infos = db_manager.query_task_info()
        logger.info(f"总共找到 {len(task_infos)} 个任务信息")
        
        if task_infos:
            # Show first few task infos
            for i, task in enumerate(task_infos[:3]):
                logger.info(f"任务信息 {i+1}: ID={task.id}, 名称={task.name}, "
                          f"点ID={task.point_id}, 楼层={task.floor}, "
                          f"关联点名={task.point_name}")
            
            if len(task_infos) > 3:
                logger.info(f"... 还有 {len(task_infos) - 3} 个任务信息")
        
        # Example of filtered queries
        logger.info("\n=== 过滤查询示例 ===")
        
        # Query path points on floor 1
        floor1_points = db_manager.query_path_points(floor=1)
        logger.info(f"楼层1的路径点数量: {len(floor1_points)}")
        
        # Query path routes on floor 1
        floor1_routes = db_manager.query_path_routes(floor=1)
        logger.info(f"楼层1的路径路线数量: {len(floor1_routes)}")
        
        # Query site info on floor 1
        floor1_sites = db_manager.query_site_info(floor=1)
        logger.info(f"楼层1的站点信息数量: {len(floor1_sites)}")
        
        logger.info("\n=== 示例完成 ===")
        logger.info("数据库查询示例执行成功！")
        
        return 0
        
    except Exception as e:
        logger.error(f"示例执行失败: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
