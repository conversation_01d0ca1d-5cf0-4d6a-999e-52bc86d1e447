from setuptools import setup, find_packages

package_name = 'rcs_backend_api'

setup(
    name=package_name,
    version='1.0.0',
    packages=find_packages(exclude=['test']),
    data_files=[
        ('share/ament_index/resource_index/packages',
            ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
    ],
    install_requires=['setuptools'],
    zip_safe=True,
    maintainer='RCS Team',
    maintainer_email='<EMAIL>',
    description='RCS Backend API package for database operations and REST API services',
    license='TODO: License declaration',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
            'database_service_node = rcs_backend_api.database_service_node:main',
            'api_server = rcs_backend_api.api_server:main',
        ],
    },
)
