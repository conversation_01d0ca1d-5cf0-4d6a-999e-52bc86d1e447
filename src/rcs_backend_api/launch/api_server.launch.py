"""
Launch file for RCS Backend API Server

This launch file starts the REST API server.
"""

from launch import LaunchDescription
from launch_ros.actions import Node
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration


def generate_launch_description():
    """Generate launch description for API server"""
    
    # Declare launch arguments
    db_path_arg = DeclareLaunchArgument(
        'db_path',
        default_value='/home/<USER>/DB_Maps/mapreflectors.db',
        description='Path to the database file'
    )
    
    host_arg = DeclareLaunchArgument(
        'host',
        default_value='0.0.0.0',
        description='Host address for the API server'
    )
    
    port_arg = DeclareLaunchArgument(
        'port',
        default_value='8080',
        description='Port for the API server'
    )
    
    log_level_arg = DeclareLaunchArgument(
        'log_level',
        default_value='info',
        description='Log level (debug, info, warn, error)'
    )
    
    # API server node
    api_server_node = Node(
        package='rcs_backend_api',
        executable='api_server',
        name='rcs_api_server_node',
        output='screen',
        parameters=[{
            'db_path': LaunchConfiguration('db_path'),
            'host': LaunchConfiguration('host'),
            'port': LaunchConfiguration('port'),
        }],
        arguments=['--ros-args', '--log-level', LaunchConfiguration('log_level')]
    )
    
    return LaunchDescription([
        db_path_arg,
        host_arg,
        port_arg,
        log_level_arg,
        api_server_node,
    ])
