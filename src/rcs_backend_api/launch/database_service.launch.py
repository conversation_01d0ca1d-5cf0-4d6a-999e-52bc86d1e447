"""
Launch file for RCS Backend API Database Service

This launch file starts the database service node.
"""

from launch import LaunchDescription
from launch_ros.actions import Node
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration


def generate_launch_description():
    """Generate launch description for database service"""
    
    # Declare launch arguments
    db_path_arg = DeclareLaunchArgument(
        'db_path',
        default_value='/home/<USER>/DB_Maps/mapreflectors.db',
        description='Path to the database file'
    )
    
    log_level_arg = DeclareLaunchArgument(
        'log_level',
        default_value='info',
        description='Log level (debug, info, warn, error)'
    )
    
    # Database service node
    database_service_node = Node(
        package='rcs_backend_api',
        executable='database_service_node',
        name='rcs_database_service_node',
        output='screen',
        parameters=[{
            'db_path': LaunchConfiguration('db_path'),
        }],
        arguments=['--ros-args', '--log-level', LaunchConfiguration('log_level')]
    )
    
    return LaunchDescription([
        db_path_arg,
        log_level_arg,
        database_service_node,
    ])
