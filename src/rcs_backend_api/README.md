# RCS Backend API

一个基于ROS2的Python功能包，提供RCS系统的数据库操作和API服务。

## 功能特性

- **数据库管理**: 提供SQLite数据库的连接和查询操作
- **ROS2服务**: 兼容现有的database_interfaces，提供ROS2服务接口
- **REST API**: 提供HTTP REST API接口，方便Web应用集成
- **数据模型**: 完整的数据类型定义，包括路径点、路径路线、站点信息等
- **错误处理**: 完善的异常处理和日志记录

## 包结构

```
src/rcs_backend_api/
├── package.xml                 # ROS2包配置文件
├── setup.py                   # Python包安装配置
├── setup.cfg                  # 安装配置
├── resource/                  # ROS2资源文件
├── rcs_backend_api/          # 主要代码目录
│   ├── __init__.py
│   ├── models/               # 数据模型
│   │   ├── __init__.py
│   │   ├── enums.py         # 枚举定义
│   │   └── data_types.py    # 数据类型定义
│   ├── database/            # 数据库模块
│   │   ├── __init__.py
│   │   ├── manager.py       # 数据库管理器
│   │   └── exceptions.py    # 异常定义
│   ├── database_service_node.py  # ROS2服务节点
│   └── api_server.py        # REST API服务器
└── test/                    # 测试文件
    └── test_database_manager.py
```

## 安装依赖

### 系统依赖
```bash
# SQLite3
sudo apt-get install sqlite3 libsqlite3-dev

# Python依赖
pip install flask flask-cors
```

### ROS2依赖
确保已安装以下ROS2包：
- `rclpy`
- `database_interfaces`
- `std_msgs`

## 编译和安装

```bash
# 在ROS2工作空间根目录下
cd /home/<USER>/workcode/rcs_new_ws

# 编译包
colcon build --packages-select rcs_backend_api

# 加载环境
source install/setup.bash
```

## 使用方法

### 1. ROS2服务节点

启动数据库服务节点：

```bash
ros2 run rcs_backend_api database_service_node
```

调用服务示例：

```bash
# 查询路径点
ros2 service call /query_path_point database_interfaces/srv/QueryPathPoint "{id: 0, floor: 1}"

# 查询路径路线
ros2 service call /query_path_route database_interfaces/srv/QueryPathRoute "{id: 0, floor: 1}"

# 查询站点信息
ros2 service call /query_site_info database_interfaces/srv/QuerySiteInfo "{id: 0, floor: 1}"
```

### 2. REST API服务器

启动API服务器：

```bash
ros2 run rcs_backend_api api_server
```

API端点：

```bash
# 健康检查
curl http://localhost:8080/health

# 查询路径点
curl "http://localhost:8080/api/path_points?floor=1"
curl "http://localhost:8080/api/path_points?id=1"

# 查询路径路线
curl "http://localhost:8080/api/path_routes?floor=1"

# 查询站点信息
curl "http://localhost:8080/api/site_info?floor=1"

# 查询区域信息
curl "http://localhost:8080/api/area_info?floor=1"
```

### 3. 直接使用数据库管理器

```python
from rcs_backend_api.database import DatabaseManager

# 创建数据库管理器
db_manager = DatabaseManager("/path/to/database.db")

# 初始化数据库
if db_manager.initialize():
    # 查询路径点
    path_points = db_manager.query_path_points(floor=1)
    
    # 查询路径路线
    path_routes = db_manager.query_path_routes(floor=1)
    
    # 查询站点信息
    site_infos = db_manager.query_site_info(floor=1)
```

## 数据模型

### 主要数据类型

- `PathPoint`: 路径点
- `PathRoute`: 路径路线
- `SiteInfo`: 站点信息
- `AreaInfo`: 区域信息
- `SpecialSite`: 特殊站点
- `TaskInfo`: 任务信息
- `LayerSwitchPoint`: 楼层切换点
- `LayerSwitchConnection`: 楼层切换连接

### 枚举类型

- `PointProperty`: 路径点属性（主节点、子节点、子节点入口）
- `RouteType`: 路径路线类型（直线、曲线）
- `RouteProperty`: 路径路线属性（单向、双向）

## 测试

运行单元测试：

```bash
# 在包目录下
python -m pytest test/

# 或者运行特定测试
python -m unittest test.test_database_manager
```

## 配置

### 数据库路径
默认数据库路径为：`/home/<USER>/DB_Maps/mapreflectors.db`

可以通过环境变量或配置文件修改：

```bash
export RCS_DB_PATH="/path/to/your/database.db"
```

### API服务器配置
- 默认主机：`0.0.0.0`
- 默认端口：`8080`

## 日志

包使用Python标准logging模块，日志级别可通过环境变量设置：

```bash
export PYTHONPATH=/path/to/your/workspace/src
export ROS_LOG_LEVEL=INFO
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库文件路径是否正确
   - 确保有读写权限

2. **ROS2服务启动失败**
   - 确保已正确安装database_interfaces包
   - 检查ROS2环境是否正确加载

3. **API服务器启动失败**
   - 确保Flask已安装：`pip install flask flask-cors`
   - 检查端口是否被占用

## 开发

### 添加新的查询方法

1. 在`DatabaseManager`类中添加新的查询方法
2. 在`DatabaseServiceNode`中添加对应的ROS2服务处理
3. 在`APIServer`中添加对应的REST API端点
4. 添加相应的测试用例

### 代码风格

项目遵循PEP 8代码风格，建议使用以下工具：

```bash
# 代码格式化
black rcs_backend_api/

# 代码检查
flake8 rcs_backend_api/
```

## 许可证

TODO: 添加许可证信息
