"""
Repository classes for RCS Backend API

This module contains repository classes that handle database operations
for each entity type using SQLAlchemy ORM.
"""

from typing import List
from sqlalchemy.orm import Session

from .models import PathPoint, PathRoute, SiteInfo, AreaInfo, SpecialSite, AreaPoint
from ..models.data_types import (
    PathPoint as PathPointDTO, PathRoute as PathRouteDTO,
    SiteInfo as SiteInfoDTO, AreaInfo as AreaInfoDTO,
    SpecialSite as SpecialSiteDTO, TaskInfo as TaskInfoDTO,
    Point2D, PointProperty, RouteType, RouteProperty
)


class BaseRepository:
    """基础仓储类"""

    def __init__(self, model_class):
        self.model_class = model_class


class PathPointRepository(BaseRepository):
    """路径点仓储"""

    def __init__(self):
        super().__init__(PathPoint)

    def get_all(self, session: Session, id: int = 0, floor: int = 0) -> List[PathPointDTO]:
        """获取所有路径点"""
        query = session.query(PathPoint)

        if id > 0:
            query = query.filter(PathPoint.id == id)
        if floor > 0:
            query = query.filter(PathPoint.floor == floor)

        results = query.all()

        return [
            PathPointDTO(
                id=row.id,
                name=row.name,
                x=row.x,
                y=row.y,
                point_property=PointProperty(row.point_property),
                is_rotated_in_place=row.is_rotated_in_place,
                floor=row.floor
            )
            for row in results
        ]


class PathRouteRepository(BaseRepository):
    """路径路线仓储"""

    def __init__(self):
        super().__init__(PathRoute)

    def get_all(self, session: Session, id: int = 0, floor: int = 0) -> List[PathRouteDTO]:
        """获取所有路径路线"""
        query = session.query(PathRoute)

        if id > 0:
            query = query.filter(PathRoute.id == id)
        if floor > 0:
            query = query.filter(PathRoute.floor == floor)

        results = query.all()

        return [
            PathRouteDTO(
                id=row.id,
                name=row.name,
                type=RouteType(row.type),
                start=row.start,
                end=row.end,
                c1_x=row.c1_x,
                c1_y=row.c1_y,
                c2_x=row.c2_x,
                c2_y=row.c2_y,
                length=row.length,
                route_property=RouteProperty(row.route_property),
                floor=row.floor,
                width=row.width,
                movement=row.movement,
                max_speed=row.max_speed
            )
            for row in results
        ]


class SiteInfoRepository(BaseRepository):
    """站点信息仓储"""

    def __init__(self):
        super().__init__(SiteInfo)

    def get_all(self, session: Session, id: int = 0, floor: int = 0) -> List[SiteInfoDTO]:
        """获取所有站点信息"""
        query = session.query(SiteInfo)

        if id > 0:
            query = query.filter(SiteInfo.id == id)
        if floor > 0:
            query = query.filter(SiteInfo.floor == floor)

        results = query.all()

        return [
            SiteInfoDTO(
                id=row.id,
                name=row.name,
                point_id=row.point_id,
                site_theta=row.site_theta,
                floor=row.floor,
                operation=row.operation,
                dir=row.dir,
                param1=row.param1,
                param2=row.param2,
                param3=row.param3,
                site_point_x=row.site_point_x,
                site_point_y=row.site_point_y,
                site_point_valid=row.site_point_valid,
                other=row.other or "",
                layer=row.layer
            )
            for row in results
        ]


class AreaInfoRepository(BaseRepository):
    """区域信息仓储"""

    def __init__(self):
        super().__init__(AreaInfo)

    def get_all(self, session: Session, id: int = 0, floor: int = 0) -> List[AreaInfoDTO]:
        """获取所有区域信息"""
        query = session.query(AreaInfo)

        if id > 0:
            query = query.filter(AreaInfo.id == id)
        if floor > 0:
            query = query.filter(AreaInfo.floor == floor)

        results = query.all()

        return [
            AreaInfoDTO(
                id=row.id,
                area_mode=row.area_mode,
                area_points=self._get_area_points(session, row.area_points_id),
                area_param=row.area_param,
                floor=row.floor
            )
            for row in results
        ]

    def _get_area_points(self, session: Session, area_points_id: str) -> List[Point2D]:
        """获取区域点"""
        points = session.query(AreaPoint).filter(AreaPoint.area_points_id == area_points_id).all()
        return [Point2D(x=point.x, y=point.y) for point in points]


class SpecialSiteRepository(BaseRepository):
    """特殊站点仓储"""

    def __init__(self):
        super().__init__(SpecialSite)

    def get_all(self, session: Session, id: int = 0, floor: int = 0, site_nature: int = -1) -> List[SpecialSiteDTO]:
        """获取所有特殊站点"""
        query = session.query(SpecialSite)

        if id > 0:
            query = query.filter(SpecialSite.id == id)
        if floor > 0:
            query = query.filter(SpecialSite.floor == floor)
        if site_nature >= 0:
            query = query.filter(SpecialSite.site_nature == site_nature)

        results = query.all()

        return [
            SpecialSiteDTO(
                id=row.id,
                name=row.name,
                site_nature=row.site_nature,
                site_point_name=row.site_point_name,
                site_theta=row.site_theta,
                floor=row.floor
            )
            for row in results
        ]


class TaskInfoRepository(BaseRepository):
    """任务信息仓储"""

    def __init__(self):
        super().__init__(SiteInfo)

    def get_all(self, session: Session, id: int = 0, floor: int = 0) -> List[TaskInfoDTO]:
        """获取所有任务信息（联查site_info和path_point表）"""
        query = session.query(SiteInfo, PathPoint).outerjoin(
            PathPoint, SiteInfo.point_id == PathPoint.id
        )

        if id > 0:
            query = query.filter(SiteInfo.id == id)
        if floor > 0:
            query = query.filter(SiteInfo.floor == floor)

        results = query.all()

        return [
            TaskInfoDTO(
                # 任务信息
                id=site.id,
                name=site.name,
                point_id=site.point_id,
                site_theta=site.site_theta,
                floor=site.floor,
                operation=site.operation,
                dir=site.dir,
                param1=site.param1,
                param2=site.param2,
                param3=site.param3,
                site_point_x=site.site_point_x,
                site_point_y=site.site_point_y,
                site_point_valid=site.site_point_valid,
                other=site.other or "",
                layer=site.layer,

                # 关联的路径点信息
                point_name=point.name if point else "",
                point_x=point.x if point else 0.0,
                point_y=point.y if point else 0.0,
                point_floor=point.floor if point else 0,
                point_property=PointProperty(point.point_property) if point else PointProperty.PRI_POINT,
                point_is_rotated_in_place=point.is_rotated_in_place if point else False
            )
            for site, point in results
        ]
