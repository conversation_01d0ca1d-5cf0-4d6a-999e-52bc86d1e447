"""
Database Manager for RCS Backend API

This module provides the main database management functionality,
using SQLAlchemy ORM for better maintainability and extensibility.
"""

import logging
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError

from .models import Base
from .repositories import (
    PathPointRepository, PathRouteRepository, SiteInfoRepository,
    AreaInfoRepository, SpecialSiteRepository, TaskInfoRepository
)
from .exceptions import QueryError


class DatabaseManager:
    """数据库管理器类，使用SQLAlchemy ORM"""

    def __init__(self, db_path: str):
        """初始化数据库管理器"""
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)

        # 创建数据库引擎
        self.engine = create_engine(f"sqlite:///{db_path}", echo=False)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)

        # 初始化仓储
        self.path_point_repo = PathPointRepository()
        self.path_route_repo = PathRouteRepository()
        self.site_info_repo = SiteInfoRepository()
        self.area_info_repo = AreaInfoRepository()
        self.special_site_repo = SpecialSiteRepository()
        self.task_info_repo = TaskInfoRepository()

    def initialize(self) -> bool:
        """初始化数据库连接和表结构"""
        try:
            self.logger.info(f"初始化数据库连接: {self.db_path}")

            # 创建所有表
            Base.metadata.create_all(bind=self.engine)

            # 测试连接
            with self.get_session() as session:
                session.execute(text("SELECT 1"))

            self.logger.info("数据库初始化成功")
            return True

        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            return False

    def get_session(self) -> Session:
        """获取数据库会话"""
        return self.SessionLocal()

    # 查询方法委托给仓储
    def query_path_points(self, id: int = 0, floor: int = 0):
        """查询路径点"""
        with self.get_session() as session:
            return self.path_point_repo.get_all(session, id=id, floor=floor)

    def query_path_routes(self, id: int = 0, floor: int = 0):
        """查询路径路线"""
        with self.get_session() as session:
            return self.path_route_repo.get_all(session, id=id, floor=floor)

    def query_site_info(self, id: int = 0, floor: int = 0):
        """查询站点信息"""
        with self.get_session() as session:
            return self.site_info_repo.get_all(session, id=id, floor=floor)

    def query_area_info(self, id: int = 0, floor: int = 0):
        """查询区域信息"""
        with self.get_session() as session:
            return self.area_info_repo.get_all(session, id=id, floor=floor)

    def query_special_sites(self, id: int = 0, floor: int = 0, site_nature: int = -1):
        """查询特殊站点"""
        with self.get_session() as session:
            return self.special_site_repo.get_all(session, id=id, floor=floor, site_nature=site_nature)

    def query_task_info(self, id: int = 0, floor: int = 0):
        """查询任务信息"""
        with self.get_session() as session:
            return self.task_info_repo.get_all(session, id=id, floor=floor)

    def execute_custom_query(self, query: str, params: dict = None):
        """执行自定义SQL查询"""
        try:
            with self.get_session() as session:
                result = session.execute(text(query), params or {})
                return result.fetchall()
        except SQLAlchemyError as e:
            self.logger.error(f"执行自定义查询失败: {e}")
            raise QueryError(f"查询失败: {e}")
