"""
Database Manager for RCS Backend API

This module provides the main database management functionality,
using SQLAlchemy ORM for better maintainability and extensibility.
"""

import logging
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError

from .models import Base
from .repositories import (
    PathPointRepository, PathRouteRepository, SiteInfoRepository,
    AreaInfoRepository, SpecialSiteRepository, TaskInfoRepository
)
from .exceptions import QueryError


class DatabaseManager:
    """数据库管理器类，使用SQLAlchemy ORM"""

    def __init__(self, db_path: str):
        """初始化数据库管理器"""
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)

        # 创建数据库引擎
        self.engine = create_engine(f"sqlite:///{db_path}", echo=False)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)

        # 初始化仓储
        self.path_point_repo = PathPointRepository()
        self.path_route_repo = PathRouteRepository()
        self.site_info_repo = SiteInfoRepository()
        self.area_info_repo = AreaInfoRepository()
        self.special_site_repo = SpecialSiteRepository()
        self.task_info_repo = TaskInfoRepository()

    def initialize(self) -> bool:
        """初始化数据库连接和表结构"""
        try:
            self.logger.info(f"初始化数据库连接: {self.db_path}")

            # 创建所有表
            Base.metadata.create_all(bind=self.engine)

            # 测试连接
            with self.get_session() as session:
                session.execute(text("SELECT 1"))

            self.logger.info("数据库初始化成功")
            return True

        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            return False

    def get_session(self) -> Session:
        """获取数据库会话"""
        return self.SessionLocal()

    # 查询方法委托给仓储
    def query_path_points(self, id: int = 0, floor: int = 0):
        """查询路径点"""
        with self.get_session() as session:
            return self.path_point_repo.get_all(session, id=id, floor=floor)

    def query_path_routes(self, id: int = 0, floor: int = 0):
        """查询路径路线"""
        with self.get_session() as session:
            return self.path_route_repo.get_all(session, id=id, floor=floor)

    def query_site_info(self, id: int = 0, floor: int = 0):
        """查询站点信息"""
        with self.get_session() as session:
            return self.site_info_repo.get_all(session, id=id, floor=floor)

    def query_area_info(self, id: int = 0, floor: int = 0):
        """查询区域信息"""
        with self.get_session() as session:
            return self.area_info_repo.get_all(session, id=id, floor=floor)

    def query_special_sites(self, id: int = 0, floor: int = 0, site_nature: int = -1):
        """查询特殊站点"""
        with self.get_session() as session:
            return self.special_site_repo.get_all(session, id=id, floor=floor, site_nature=site_nature)

    def query_task_info(self, id: int = 0, floor: int = 0):
        """查询任务信息"""
        with self.get_session() as session:
            return self.task_info_repo.get_all(session, id=id, floor=floor)

    def execute_custom_query(self, query: str, params: dict = None):
        """执行自定义SQL查询"""
        try:
            with self.get_session() as session:
                result = session.execute(text(query), params or {})
                return result.fetchall()
        except SQLAlchemyError as e:
            self.logger.error(f"执行自定义查询失败: {e}")
            raise QueryError(f"查询失败: {e}")
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()

                # 构建查询条件
                conditions = []
                params = []

                if id > 0:
                    conditions.append("id = ?")
                    params.append(id)

                if floor > 0:
                    conditions.append("floor = ?")
                    params.append(floor)

                where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""

                query = f"SELECT * FROM path_point{where_clause}"
                cursor.execute(query, params)

                rows = cursor.fetchall()

                return [
                    PathPoint(
                        id=row['id'],
                        name=row['name'],
                        x=row['x'],
                        y=row['y'],
                        point_property=PointProperty(row['point_property']),
                        is_rotated_in_place=bool(row['is_rotated_in_place']),
                        floor=row['floor']
                    )
                    for row in rows
                ]

        except Exception as e:
            self.logger.error(f"查询路径点失败: {e}")
            raise QueryError(f"查询路径点失败: {e}")

    def query_path_routes(self, id: int = 0, floor: int = 0) -> List[PathRoute]:
        """
        查询路径路线

        Args:
            id: 路线ID，0表示查询所有
            floor: 楼层，0表示查询所有楼层

        Returns:
            List[PathRoute]: 路径路线列表
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()

                # 构建查询条件
                conditions = []
                params = []

                if id > 0:
                    conditions.append("id = ?")
                    params.append(id)

                if floor > 0:
                    conditions.append("floor = ?")
                    params.append(floor)

                where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""

                query = f"SELECT * FROM path_route{where_clause}"
                cursor.execute(query, params)

                rows = cursor.fetchall()

                return [
                    PathRoute(
                        id=row['id'],
                        name=row['name'],
                        type=RouteType(row['type']),
                        start=row['start'],
                        end=row['end'],
                        c1_x=row['c1_x'],
                        c1_y=row['c1_y'],
                        c2_x=row['c2_x'],
                        c2_y=row['c2_y'],
                        length=row['length'],
                        route_property=RouteProperty(row['route_property']),
                        floor=row['floor'],
                        width=row['width'],
                        movement=bool(row['movement']),
                        max_speed=row['max_speed']
                    )
                    for row in rows
                ]

        except Exception as e:
            self.logger.error(f"查询路径路线失败: {e}")
            raise QueryError(f"查询路径路线失败: {e}")

    def query_site_info(self, id: int = 0, floor: int = 0) -> List[SiteInfo]:
        """
        查询站点信息

        Args:
            id: 站点ID，0表示查询所有
            floor: 楼层，0表示查询所有楼层

        Returns:
            List[SiteInfo]: 站点信息列表
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()

                # 构建查询条件
                conditions = []
                params = []

                if id > 0:
                    conditions.append("id = ?")
                    params.append(id)

                if floor > 0:
                    conditions.append("floor = ?")
                    params.append(floor)

                where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""

                query = f"SELECT * FROM site_info{where_clause}"
                cursor.execute(query, params)

                rows = cursor.fetchall()

                return [
                    SiteInfo(
                        id=row['id'],
                        name=row['name'],
                        point_id=row['point_id'],
                        site_theta=row['site_theta'],
                        floor=row['floor'],
                        operation=row['operation'],
                        dir=bool(row['dir']),
                        param1=row['param1'],
                        param2=row['param2'],
                        param3=row['param3'],
                        site_point_x=row['site_point_x'],
                        site_point_y=row['site_point_y'],
                        site_point_valid=bool(row['site_point_valid']),
                        other=row['other'] or "",
                        layer=row['layer']
                    )
                    for row in rows
                ]

        except Exception as e:
            self.logger.error(f"查询站点信息失败: {e}")
            raise QueryError(f"查询站点信息失败: {e}")

    def _parse_area_points(self, area_points_id: str) -> List[Point2D]:
        """
        解析区域点数据

        Args:
            area_points_id: 区域点ID

        Returns:
            List[Point2D]: 区域点列表
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()

                query = "SELECT x, y FROM area_point_tb WHERE area_points_id = ?"
                cursor.execute(query, (area_points_id,))

                rows = cursor.fetchall()

                return [Point2D(x=row['x'], y=row['y']) for row in rows]

        except Exception as e:
            self.logger.error(f"解析区域点失败: {e}")
            return []

    def query_area_info(self, id: int = 0, floor: int = 0) -> List[AreaInfo]:
        """
        查询区域信息

        Args:
            id: 区域ID，0表示查询所有
            floor: 楼层，0表示查询所有楼层

        Returns:
            List[AreaInfo]: 区域信息列表
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()

                # 构建查询条件
                conditions = []
                params = []

                if id > 0:
                    conditions.append("id = ?")
                    params.append(id)

                if floor > 0:
                    conditions.append("floor = ?")
                    params.append(floor)

                where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""

                query = f"SELECT * FROM area_info{where_clause}"
                cursor.execute(query, params)

                rows = cursor.fetchall()

                return [
                    AreaInfo(
                        id=row['id'],
                        area_mode=row['area_mode'],
                        area_points=self._parse_area_points(row['area_points_id']),
                        area_param=row['area_param'],
                        floor=row['floor']
                    )
                    for row in rows
                ]

        except Exception as e:
            self.logger.error(f"查询区域信息失败: {e}")
            raise QueryError(f"查询区域信息失败: {e}")

    def query_special_sites(self, id: int = 0, floor: int = 0, site_nature: int = -1) -> List[SpecialSite]:
        """
        查询特殊站点

        Args:
            id: 站点ID，0表示查询所有
            floor: 楼层，0表示查询所有楼层
            site_nature: 站点性质，-1表示查询所有

        Returns:
            List[SpecialSite]: 特殊站点列表
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()

                # 构建查询条件
                conditions = []
                params = []

                if id > 0:
                    conditions.append("id = ?")
                    params.append(id)

                if floor > 0:
                    conditions.append("floor = ?")
                    params.append(floor)

                if site_nature >= 0:
                    conditions.append("site_nature = ?")
                    params.append(site_nature)

                where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""

                query = f"SELECT * FROM special_site_tb{where_clause}"
                cursor.execute(query, params)

                rows = cursor.fetchall()

                return [
                    SpecialSite(
                        id=row['id'],
                        name=row['name'],
                        site_nature=row['site_nature'],
                        site_point_name=row['site_point_name'],
                        site_theta=row['site_theta'],
                        floor=row['floor']
                    )
                    for row in rows
                ]

        except Exception as e:
            self.logger.error(f"查询特殊站点失败: {e}")
            raise QueryError(f"查询特殊站点失败: {e}")

    def _extract_point_number(self, point_name: str) -> int:
        """
        从点名中提取数字部分

        Args:
            point_name: 点名，如 "Point-10001"

        Returns:
            int: 提取的数字，如 10001
        """
        import re
        match = re.search(r'(\d+)', point_name)
        return int(match.group(1)) if match else 0

    def query_task_info(self, id: int = 0, floor: int = 0) -> List[TaskInfo]:
        """
        查询任务信息（联查site_info和path_point表）

        Args:
            id: 任务ID，0表示查询所有
            floor: 楼层，0表示查询所有楼层

        Returns:
            List[TaskInfo]: 任务信息列表
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()

                # 构建查询条件
                conditions = []
                params = []

                if id > 0:
                    conditions.append("s.id = ?")
                    params.append(id)

                if floor > 0:
                    conditions.append("s.floor = ?")
                    params.append(floor)

                where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""

                # 联查site_info和path_point表
                query = f"""
                    SELECT s.*, p.name as point_name, p.x as point_x, p.y as point_y,
                           p.floor as point_floor, p.point_property, p.is_rotated_in_place as point_is_rotated_in_place
                    FROM site_info s
                    LEFT JOIN path_point p ON s.point_id = p.id
                    {where_clause}
                """
                cursor.execute(query, params)

                rows = cursor.fetchall()

                return [
                    TaskInfo(
                        # 任务信息
                        id=row['id'],
                        name=row['name'],
                        point_id=row['point_id'],
                        site_theta=row['site_theta'],
                        floor=row['floor'],
                        operation=row['operation'],
                        dir=bool(row['dir']),
                        param1=row['param1'],
                        param2=row['param2'],
                        param3=row['param3'],
                        site_point_x=row['site_point_x'],
                        site_point_y=row['site_point_y'],
                        site_point_valid=bool(row['site_point_valid']),
                        other=row['other'] or "",
                        layer=row['layer'],

                        # 关联的路径点信息
                        point_name=row['point_name'] or "",
                        point_x=row['point_x'] or 0.0,
                        point_y=row['point_y'] or 0.0,
                        point_floor=row['point_floor'] or 0,
                        point_property=PointProperty(row['point_property']) if row['point_property'] is not None else PointProperty.PRI_POINT,
                        point_is_rotated_in_place=bool(row['point_is_rotated_in_place']) if row['point_is_rotated_in_place'] is not None else False
                    )
                    for row in rows
                ]

        except Exception as e:
            self.logger.error(f"查询任务信息失败: {e}")
            raise QueryError(f"查询任务信息失败: {e}")
