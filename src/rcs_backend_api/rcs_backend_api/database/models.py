"""
SQLAlchemy models for RCS Backend API

This module contains all the SQLAlchemy ORM models for the database tables.
"""

from sqlalchemy import Column, Integer, String, Float, Boolean, Text, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

Base = declarative_base()


class PathPoint(Base):
    """路径点表"""
    __tablename__ = "path_point"

    id = Column(Integer, primary_key=True)
    name = Column(String, nullable=False)
    x = Column(Float, nullable=False)
    y = Column(Float, nullable=False)
    point_property = Column(Integer, nullable=False)
    is_rotated_in_place = Column(Boolean, nullable=False)
    floor = Column(Integer, nullable=False)


class PathRoute(Base):
    """路径路线表"""
    __tablename__ = "path_route"

    id = Column(Integer, primary_key=True)
    name = Column(String, nullable=False)
    type = Column(Integer, nullable=False)
    start = Column(Integer, nullable=False)
    end = Column(Integer, nullable=False)
    c1_x = Column(Float, nullable=False)
    c1_y = Column(Float, nullable=False)
    c2_x = Column(Float, nullable=False)
    c2_y = Column(Float, nullable=False)
    length = Column(Float, nullable=False)
    route_property = Column(Integer, nullable=False)
    floor = Column(Integer, nullable=False)
    width = Column(Float, nullable=False)
    movement = Column(Boolean, nullable=False)
    max_speed = Column(Float, nullable=False)


class SiteInfo(Base):
    """站点信息表"""
    __tablename__ = "site_info"

    id = Column(Integer, primary_key=True)
    name = Column(String, nullable=False)
    point_id = Column(Integer, nullable=False)
    site_theta = Column(Float, nullable=False)
    floor = Column(Integer, nullable=False)
    operation = Column(String, nullable=False)
    dir = Column(Boolean, nullable=False)
    param1 = Column(Float, nullable=False)
    param2 = Column(Float, nullable=False)
    param3 = Column(Float, nullable=False)
    site_point_x = Column(Float, nullable=False)
    site_point_y = Column(Float, nullable=False)
    site_point_valid = Column(Boolean, nullable=False)
    other = Column(Text)
    layer = Column(Integer, nullable=False)


class AreaInfo(Base):
    """区域信息表"""
    __tablename__ = "area_info"

    id = Column(Integer, primary_key=True)
    area_mode = Column(Integer, nullable=False)
    area_points_id = Column(String, nullable=False)
    area_param = Column(Integer, nullable=False)
    floor = Column(Integer, nullable=False)


class AreaPoint(Base):
    """区域点表"""
    __tablename__ = "area_point_tb"

    id = Column(Integer, primary_key=True, autoincrement=True)
    area_points_id = Column(String, nullable=False)
    x = Column(Float, nullable=False)
    y = Column(Float, nullable=False)


class SpecialSite(Base):
    """特殊站点表"""
    __tablename__ = "special_site_tb"

    id = Column(Integer, primary_key=True)
    name = Column(String, nullable=False)
    site_nature = Column(Integer, nullable=False)
    site_point_name = Column(String, nullable=False)
    site_theta = Column(Float, nullable=False)
    floor = Column(Integer, nullable=False)


class LayerSwitchPoint(Base):
    """楼层切换点表"""
    __tablename__ = "layer_switch_point"

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String, nullable=False)
    nature = Column(Integer, nullable=False)
    point_id = Column(Integer, nullable=False)
    floor = Column(Integer, nullable=False)
    in_theta = Column(Float, nullable=False)
    out_theta = Column(Float, nullable=False)
    is_active = Column(Boolean, nullable=False, default=True)


class LayerSwitchConnection(Base):
    """楼层切换连接表"""
    __tablename__ = "layer_switch_connection"

    id = Column(Integer, primary_key=True, autoincrement=True)
    from_switch_id = Column(Integer, ForeignKey('layer_switch_point.id'), nullable=False)
    to_switch_id = Column(Integer, ForeignKey('layer_switch_point.id'), nullable=False)
    is_active = Column(Boolean, nullable=False, default=True)

    # 关系
    from_switch = relationship("LayerSwitchPoint", foreign_keys=[from_switch_id])
    to_switch = relationship("LayerSwitchPoint", foreign_keys=[to_switch_id])
