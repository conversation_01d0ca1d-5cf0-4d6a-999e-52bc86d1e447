"""
Database Service Node for RCS Backend API

This module provides a ROS2 service node that exposes database operations
as ROS2 services, compatible with the existing database_interfaces.
"""

import rclpy
from rclpy.node import Node
import logging
from typing import List

# ROS2 service interfaces
from database_interfaces.srv import (
    QueryPathPoint, QueryPathRoute, QuerySiteInfo, QueryAreaInfo,
    QuerySpecialSite, QueryTaskInfo, QueryLayerSwitchPoint,
    QueryLayerSwitchConnection, QueryLayerSwitchConnectionInfo
)

# ROS2 message types
from database_interfaces.msg import (
    PathPoint as PathPointMsg, PathRoute as PathRouteMsg,
    SiteInfo as SiteInfoMsg, AreaInfo as AreaInfoMsg,
    SpecialSite as SpecialSiteMsg, TaskInfo as TaskInfoMsg,
    LayerSwitchPoint as LayerSwitchPointMsg,
    LayerSwitchConnection as LayerSwitchConnectionMsg,
    LayerSwitchConnectionInfo as LayerSwitchConnectionInfoMsg,
    Point2D as Point2DMsg, ReachablePoint as ReachablePointMsg,
    PointProperty as PointPropertyMsg, RouteType as RouteTypeMsg,
    RouteProperty as RoutePropertyMsg
)

from .database import DatabaseManager
from .models import PathPoint, PathRoute, SiteInfo, AreaInfo


class DatabaseServiceNode(Node):
    """ROS2数据库服务节点"""

    def __init__(self):
        super().__init__('rcs_database_service_node')

        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

        self.logger.info("=== RCS数据库服务节点启动 ===")

        # 初始化数据库
        db_path = "/home/<USER>/DB_Maps/mapreflectors.db"
        self.db_manager = DatabaseManager(db_path)

        if not self.db_manager.initialize():
            self.logger.error("数据库初始化失败")
            return

        # 创建服务
        self._create_services()

        self.logger.info("所有数据库服务已启动")

    def _create_services(self):
        """创建所有ROS2服务"""

        # 路径点查询服务
        self.path_point_service = self.create_service(
            QueryPathPoint,
            'query_path_point',
            self._handle_query_path_point
        )

        # 路径路线查询服务
        self.path_route_service = self.create_service(
            QueryPathRoute,
            'query_path_route',
            self._handle_query_path_route
        )

        # 站点信息查询服务
        self.site_info_service = self.create_service(
            QuerySiteInfo,
            'query_site_info',
            self._handle_query_site_info
        )

        # 区域信息查询服务
        self.area_info_service = self.create_service(
            QueryAreaInfo,
            'query_area_info',
            self._handle_query_area_info
        )

        # TODO: 添加其他服务

    def _handle_query_path_point(self, request, response):
        """处理路径点查询请求"""
        try:
            self.logger.info(f"查询路径点: id={request.id}, floor={request.floor}")

            # 调用数据库管理器查询
            path_points = self.db_manager.query_path_points(
                id=request.id,
                floor=request.floor
            )

            # 转换为ROS2消息格式
            response.path_points = [
                self._convert_path_point_to_msg(pp) for pp in path_points
            ]
            response.success = True
            response.message = f"成功查询到 {len(path_points)} 个路径点"

        except Exception as e:
            self.logger.error(f"查询路径点失败: {e}")
            response.success = False
            response.message = f"查询失败: {str(e)}"
            response.path_points = []

        return response

    def _handle_query_path_route(self, request, response):
        """处理路径路线查询请求"""
        try:
            self.logger.info(f"查询路径路线: id={request.id}, floor={request.floor}")

            # 调用数据库管理器查询
            path_routes = self.db_manager.query_path_routes(
                id=request.id,
                floor=request.floor
            )

            # 转换为ROS2消息格式
            response.path_routes = [
                self._convert_path_route_to_msg(pr) for pr in path_routes
            ]
            response.success = True
            response.message = f"成功查询到 {len(path_routes)} 条路径路线"

        except Exception as e:
            self.logger.error(f"查询路径路线失败: {e}")
            response.success = False
            response.message = f"查询失败: {str(e)}"
            response.path_routes = []

        return response

    def _handle_query_site_info(self, request, response):
        """处理站点信息查询请求"""
        try:
            self.logger.info(f"查询站点信息: id={request.id}, floor={request.floor}")

            # 调用数据库管理器查询
            site_infos = self.db_manager.query_site_info(
                id=request.id,
                floor=request.floor
            )

            # 转换为ROS2消息格式
            response.site_infos = [
                self._convert_site_info_to_msg(si) for si in site_infos
            ]
            response.success = True
            response.message = f"成功查询到 {len(site_infos)} 个站点信息"

        except Exception as e:
            self.logger.error(f"查询站点信息失败: {e}")
            response.success = False
            response.message = f"查询失败: {str(e)}"
            response.site_infos = []

        return response

    def _handle_query_area_info(self, request, response):
        """处理区域信息查询请求"""
        try:
            self.logger.info(f"查询区域信息: id={request.id}, floor={request.floor}")

            # 调用数据库管理器查询
            area_infos = self.db_manager.query_area_info(
                id=request.id,
                floor=request.floor
            )

            # 转换为ROS2消息格式
            response.area_infos = [
                self._convert_area_info_to_msg(ai) for ai in area_infos
            ]
            response.success = True
            response.message = f"成功查询到 {len(area_infos)} 个区域信息"

        except Exception as e:
            self.logger.error(f"查询区域信息失败: {e}")
            response.success = False
            response.message = f"查询失败: {str(e)}"
            response.area_infos = []

        return response

    def _convert_path_point_to_msg(self, path_point: PathPoint) -> PathPointMsg:
        """将PathPoint对象转换为ROS2消息"""
        msg = PathPointMsg()
        msg.id = path_point.id
        msg.name = path_point.name
        msg.x = path_point.x
        msg.y = path_point.y

        # 设置点属性
        point_property_msg = PointPropertyMsg()
        point_property_msg.value = int(path_point.point_property)
        msg.point_property = point_property_msg

        msg.is_rotated_in_place = path_point.is_rotated_in_place
        msg.floor = path_point.floor

        return msg

    def _convert_path_route_to_msg(self, path_route: PathRoute) -> PathRouteMsg:
        """将PathRoute对象转换为ROS2消息"""
        msg = PathRouteMsg()
        msg.id = path_route.id
        msg.name = path_route.name

        # 设置路线类型
        route_type_msg = RouteTypeMsg()
        route_type_msg.value = int(path_route.type)
        msg.type = route_type_msg

        msg.start = path_route.start
        msg.end = path_route.end
        msg.c1_x = path_route.c1_x
        msg.c1_y = path_route.c1_y
        msg.c2_x = path_route.c2_x
        msg.c2_y = path_route.c2_y
        msg.length = path_route.length

        # 设置路线属性
        route_property_msg = RoutePropertyMsg()
        route_property_msg.value = int(path_route.route_property)
        msg.route_property = route_property_msg

        msg.floor = path_route.floor
        msg.width = path_route.width
        msg.movement = path_route.movement
        msg.max_speed = path_route.max_speed

        return msg

    def _convert_site_info_to_msg(self, site_info: SiteInfo) -> SiteInfoMsg:
        """将SiteInfo对象转换为ROS2消息"""
        msg = SiteInfoMsg()
        msg.id = site_info.id
        msg.name = site_info.name
        msg.point_id = site_info.point_id
        msg.site_theta = site_info.site_theta
        msg.floor = site_info.floor
        msg.operation = site_info.operation
        msg.dir = site_info.dir
        msg.param1 = site_info.param1
        msg.param2 = site_info.param2
        msg.param3 = site_info.param3
        msg.site_point_x = site_info.site_point_x
        msg.site_point_y = site_info.site_point_y
        msg.site_point_valid = site_info.site_point_valid
        msg.other = site_info.other
        msg.layer = site_info.layer

        return msg

    def _convert_area_info_to_msg(self, area_info: AreaInfo) -> AreaInfoMsg:
        """将AreaInfo对象转换为ROS2消息"""
        msg = AreaInfoMsg()
        msg.id = area_info.id
        msg.area_mode = area_info.area_mode

        # 转换区域点列表
        msg.area_points = [
            self._convert_point2d_to_msg(point) for point in area_info.area_points
        ]

        msg.area_param = area_info.area_param
        msg.floor = area_info.floor

        return msg

    def _convert_point2d_to_msg(self, point) -> Point2DMsg:
        """将Point2D对象转换为ROS2消息"""
        msg = Point2DMsg()
        msg.x = point.x
        msg.y = point.y
        return msg


def main(args=None):
    """主函数"""
    rclpy.init(args=args)

    try:
        node = DatabaseServiceNode()

        if node.db_manager:  # 确保数据库初始化成功
            logging.info("RCS数据库服务开始运行...")
            rclpy.spin(node)
        else:
            logging.error("数据库初始化失败，服务无法启动")

    except Exception as e:
        logging.error(f"服务运行异常: {e}")
        return 1
    finally:
        rclpy.shutdown()
        logging.info("RCS数据库服务已关闭")

    return 0


if __name__ == '__main__':
    main()
