"""
FastAPI Server for RCS Backend API

This module provides a FastAPI server that exposes database operations
as HTTP endpoints, allowing web applications to interact with the RCS database.
"""

import logging
from typing import List, Optional
from contextlib import asynccontextmanager

try:
    from fastapi import FastAPI, HTTPException, Query
    from fastapi.middleware.cors import CORSMiddleware
    from pydantic import BaseModel
    import uvicorn
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False
    logging.warning("FastAPI not available. REST API server will not work.")

from .database import DatabaseManager


class APIServer:
    """REST API服务器类"""

    def __init__(self, db_path: str, host: str = '0.0.0.0', port: int = 8080):
        """
        初始化API服务器

        Args:
            db_path: 数据库文件路径
            host: 服务器主机地址
            port: 服务器端口
        """
        if not FLASK_AVAILABLE:
            raise ImportError("Flask is required for REST API server")

        self.host = host
        self.port = port

        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

        # 初始化数据库
        self.db_manager = DatabaseManager(db_path)
        if not self.db_manager.initialize():
            raise RuntimeError("数据库初始化失败")

        # 创建Flask应用
        self.app = Flask(__name__)
        CORS(self.app)  # 启用跨域支持

        # 注册路由
        self._register_routes()

        self.logger.info("API服务器初始化完成")

    def _register_routes(self):
        """注册所有API路由"""

        @self.app.route('/health', methods=['GET'])
        def health_check():
            """健康检查端点"""
            return jsonify({
                'status': 'healthy',
                'message': 'RCS Backend API is running'
            })

        @self.app.route('/api/path_points', methods=['GET'])
        def get_path_points():
            """获取路径点"""
            try:
                # 获取查询参数
                point_id = request.args.get('id', 0, type=int)
                floor = request.args.get('floor', 0, type=int)

                # 查询数据库
                path_points = self.db_manager.query_path_points(id=point_id, floor=floor)

                # 转换为字典格式
                result = [self._convert_to_dict(pp) for pp in path_points]

                return jsonify({
                    'success': True,
                    'data': result,
                    'count': len(result),
                    'message': f'成功查询到 {len(result)} 个路径点'
                })

            except Exception as e:
                self.logger.error(f"查询路径点失败: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e),
                    'message': '查询路径点失败'
                }), 500

        @self.app.route('/api/path_routes', methods=['GET'])
        def get_path_routes():
            """获取路径路线"""
            try:
                # 获取查询参数
                route_id = request.args.get('id', 0, type=int)
                floor = request.args.get('floor', 0, type=int)

                # 查询数据库
                path_routes = self.db_manager.query_path_routes(id=route_id, floor=floor)

                # 转换为字典格式
                result = [self._convert_to_dict(pr) for pr in path_routes]

                return jsonify({
                    'success': True,
                    'data': result,
                    'count': len(result),
                    'message': f'成功查询到 {len(result)} 条路径路线'
                })

            except Exception as e:
                self.logger.error(f"查询路径路线失败: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e),
                    'message': '查询路径路线失败'
                }), 500

        @self.app.route('/api/site_info', methods=['GET'])
        def get_site_info():
            """获取站点信息"""
            try:
                # 获取查询参数
                site_id = request.args.get('id', 0, type=int)
                floor = request.args.get('floor', 0, type=int)

                # 查询数据库
                site_infos = self.db_manager.query_site_info(id=site_id, floor=floor)

                # 转换为字典格式
                result = [self._convert_to_dict(si) for si in site_infos]

                return jsonify({
                    'success': True,
                    'data': result,
                    'count': len(result),
                    'message': f'成功查询到 {len(result)} 个站点信息'
                })

            except Exception as e:
                self.logger.error(f"查询站点信息失败: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e),
                    'message': '查询站点信息失败'
                }), 500

        @self.app.route('/api/area_info', methods=['GET'])
        def get_area_info():
            """获取区域信息"""
            try:
                # 获取查询参数
                area_id = request.args.get('id', 0, type=int)
                floor = request.args.get('floor', 0, type=int)

                # 查询数据库
                area_infos = self.db_manager.query_area_info(id=area_id, floor=floor)

                # 转换为字典格式
                result = [self._convert_to_dict(ai) for ai in area_infos]

                return jsonify({
                    'success': True,
                    'data': result,
                    'count': len(result),
                    'message': f'成功查询到 {len(result)} 个区域信息'
                })

            except Exception as e:
                self.logger.error(f"查询区域信息失败: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e),
                    'message': '查询区域信息失败'
                }), 500

    def _convert_to_dict(self, obj) -> Dict[str, Any]:
        """将数据对象转换为字典格式"""
        if hasattr(obj, '__dict__'):
            result = {}
            for key, value in obj.__dict__.items():
                if hasattr(value, 'value'):  # 处理枚举类型
                    result[key] = value.value
                elif hasattr(value, '__dict__'):  # 处理嵌套对象
                    result[key] = self._convert_to_dict(value)
                elif isinstance(value, list):  # 处理列表
                    result[key] = [self._convert_to_dict(item) if hasattr(item, '__dict__') else item for item in value]
                else:
                    result[key] = value
            return result
        else:
            return obj

    def run(self, debug: bool = False):
        """启动API服务器"""
        self.logger.info(f"启动API服务器: http://{self.host}:{self.port}")
        self.app.run(host=self.host, port=self.port, debug=debug)


def main(args=None):
    """主函数"""
    try:
        # 数据库路径
        db_path = "/home/<USER>/DB_Maps/mapreflectors.db"
        # 创建并启动API服务器
        server = APIServer(db_path)
        server.run(debug=True)
    except Exception as e:
        logging.error(f"API服务器启动失败: {e}")
        return 1
    return 0


if __name__ == '__main__':
    main()
