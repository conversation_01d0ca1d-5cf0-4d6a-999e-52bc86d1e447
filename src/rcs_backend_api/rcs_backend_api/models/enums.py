"""
Enumerations for RCS Backend API

This module contains all the enum definitions used in the RCS system.
"""

from enum import IntEnum


class PointProperty(IntEnum):
    """路径点属性枚举"""
    PRI_POINT = 0        # 主节点
    SUB_POINT = 1        # 子节点
    SUB_ENTER_POINT = 2  # 子节点入口

    def __str__(self):
        return self.name


class RouteType(IntEnum):
    """路径路线类型枚举"""
    ROUTE_LINE = 0   # 直线
    ROUTE_CURVE = 1  # 曲线

    def __str__(self):
        return self.name


class RouteProperty(IntEnum):
    """路径路线属性枚举"""
    SINGLE = 0   # 单向
    DOUBLE = 1   # 双向

    def __str__(self):
        return self.name


class SiteProperty(IntEnum):
    """站点属性枚举"""
    # TODO: 根据实际需求添加站点属性
    pass

    def __str__(self):
        return self.name
