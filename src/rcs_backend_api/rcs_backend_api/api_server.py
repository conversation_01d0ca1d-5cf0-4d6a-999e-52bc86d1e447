"""
FastAPI Server for RCS Backend API

This module provides a FastAPI server that exposes database operations
as HTTP endpoints, allowing web applications to interact with the RCS database.
"""

import logging
from typing import List, Optional
from contextlib import asynccontextmanager

try:
    from fastapi import FastAPI, HTTPException, Query
    from fastapi.middleware.cors import CORSMiddleware
    from pydantic import BaseModel
    import uvicorn
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False
    logging.warning("FastAPI not available. REST API server will not work.")

from .database import DatabaseManager


# Pydantic响应模型
class APIResponse(BaseModel):
    """API响应基础模型"""
    success: bool
    message: str
    data: Optional[List] = None
    count: Optional[int] = None


class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str
    message: str


# 全局数据库管理器
db_manager: Optional[DatabaseManager] = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global db_manager
    
    # 启动时初始化数据库
    db_path = "/home/<USER>/DB_Maps/mapreflectors.db"
    db_manager = DatabaseManager(db_path)
    
    if not db_manager.initialize():
        raise RuntimeError("数据库初始化失败")
    
    logging.info("FastAPI服务器启动，数据库连接成功")
    
    yield
    
    # 关闭时清理资源
    logging.info("FastAPI服务器关闭")


def create_app() -> FastAPI:
    """创建FastAPI应用"""
    if not FASTAPI_AVAILABLE:
        raise ImportError("FastAPI is required for REST API server")
    
    app = FastAPI(
        title="RCS Backend API",
        description="RCS系统数据库API服务",
        version="1.0.0",
        lifespan=lifespan
    )
    
    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    @app.get("/health", response_model=HealthResponse)
    async def health_check():
        """健康检查端点"""
        return HealthResponse(
            status="healthy",
            message="RCS Backend API is running"
        )
    
    @app.get("/api/path_points", response_model=APIResponse)
    async def get_path_points(
        id: int = Query(0, description="路径点ID，0表示查询所有"),
        floor: int = Query(0, description="楼层，0表示查询所有楼层")
    ):
        """获取路径点"""
        try:
            if not db_manager:
                raise HTTPException(status_code=500, detail="数据库未初始化")
            
            path_points = db_manager.query_path_points(id=id, floor=floor)
            
            # 转换为字典格式
            data = [
                {
                    "id": pp.id,
                    "name": pp.name,
                    "x": pp.x,
                    "y": pp.y,
                    "point_property": pp.point_property.value,
                    "is_rotated_in_place": pp.is_rotated_in_place,
                    "floor": pp.floor
                }
                for pp in path_points
            ]
            
            return APIResponse(
                success=True,
                message=f"成功查询到 {len(data)} 个路径点",
                data=data,
                count=len(data)
            )
            
        except Exception as e:
            logging.error(f"查询路径点失败: {e}")
            raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")
    
    @app.get("/api/path_routes", response_model=APIResponse)
    async def get_path_routes(
        id: int = Query(0, description="路线ID，0表示查询所有"),
        floor: int = Query(0, description="楼层，0表示查询所有楼层")
    ):
        """获取路径路线"""
        try:
            if not db_manager:
                raise HTTPException(status_code=500, detail="数据库未初始化")
            
            path_routes = db_manager.query_path_routes(id=id, floor=floor)
            
            # 转换为字典格式
            data = [
                {
                    "id": pr.id,
                    "name": pr.name,
                    "type": pr.type.value,
                    "start": pr.start,
                    "end": pr.end,
                    "c1_x": pr.c1_x,
                    "c1_y": pr.c1_y,
                    "c2_x": pr.c2_x,
                    "c2_y": pr.c2_y,
                    "length": pr.length,
                    "route_property": pr.route_property.value,
                    "floor": pr.floor,
                    "width": pr.width,
                    "movement": pr.movement,
                    "max_speed": pr.max_speed
                }
                for pr in path_routes
            ]
            
            return APIResponse(
                success=True,
                message=f"成功查询到 {len(data)} 条路径路线",
                data=data,
                count=len(data)
            )
            
        except Exception as e:
            logging.error(f"查询路径路线失败: {e}")
            raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")
    
    @app.get("/api/site_info", response_model=APIResponse)
    async def get_site_info(
        id: int = Query(0, description="站点ID，0表示查询所有"),
        floor: int = Query(0, description="楼层，0表示查询所有楼层")
    ):
        """获取站点信息"""
        try:
            if not db_manager:
                raise HTTPException(status_code=500, detail="数据库未初始化")
            
            site_infos = db_manager.query_site_info(id=id, floor=floor)
            
            # 转换为字典格式
            data = [
                {
                    "id": si.id,
                    "name": si.name,
                    "point_id": si.point_id,
                    "site_theta": si.site_theta,
                    "floor": si.floor,
                    "operation": si.operation,
                    "dir": si.dir,
                    "param1": si.param1,
                    "param2": si.param2,
                    "param3": si.param3,
                    "site_point_x": si.site_point_x,
                    "site_point_y": si.site_point_y,
                    "site_point_valid": si.site_point_valid,
                    "other": si.other,
                    "layer": si.layer
                }
                for si in site_infos
            ]
            
            return APIResponse(
                success=True,
                message=f"成功查询到 {len(data)} 个站点信息",
                data=data,
                count=len(data)
            )
            
        except Exception as e:
            logging.error(f"查询站点信息失败: {e}")
            raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")
    
    @app.get("/api/area_info", response_model=APIResponse)
    async def get_area_info(
        id: int = Query(0, description="区域ID，0表示查询所有"),
        floor: int = Query(0, description="楼层，0表示查询所有楼层")
    ):
        """获取区域信息"""
        try:
            if not db_manager:
                raise HTTPException(status_code=500, detail="数据库未初始化")
            
            area_infos = db_manager.query_area_info(id=id, floor=floor)
            
            # 转换为字典格式
            data = [
                {
                    "id": ai.id,
                    "area_mode": ai.area_mode,
                    "area_points": [{"x": p.x, "y": p.y} for p in ai.area_points],
                    "area_param": ai.area_param,
                    "floor": ai.floor
                }
                for ai in area_infos
            ]
            
            return APIResponse(
                success=True,
                message=f"成功查询到 {len(data)} 个区域信息",
                data=data,
                count=len(data)
            )
            
        except Exception as e:
            logging.error(f"查询区域信息失败: {e}")
            raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")
    
    @app.post("/api/custom_query", response_model=APIResponse)
    async def execute_custom_query(query: str, params: dict = None):
        """执行自定义SQL查询"""
        try:
            if not db_manager:
                raise HTTPException(status_code=500, detail="数据库未初始化")
            
            result = db_manager.execute_custom_query(query, params)
            
            # 转换结果为字典列表
            data = [dict(row._mapping) for row in result]
            
            return APIResponse(
                success=True,
                message=f"查询成功，返回 {len(data)} 条记录",
                data=data,
                count=len(data)
            )
            
        except Exception as e:
            logging.error(f"执行自定义查询失败: {e}")
            raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")
    
    return app


def main(args=None):
    """主函数"""
    try:
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        
        # 创建FastAPI应用
        app = create_app()
        
        # 启动服务器
        uvicorn.run(app, host="0.0.0.0", port=8080, log_level="info")
        
    except Exception as e:
        logging.error(f"API服务器启动失败: {e}")
        return 1
    
    return 0


if __name__ == '__main__':
    main()
