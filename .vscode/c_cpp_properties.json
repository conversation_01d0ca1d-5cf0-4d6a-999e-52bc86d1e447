{"configurations": [{"browse": {"databaseFilename": "${default}", "limitSymbolsToIncludedHeaders": false}, "includePath": ["/home/<USER>/workcode/rcs_new_ws/install/vcu_interfaces/include/**", "/home/<USER>/workcode/rcs_new_ws/install/slam_center_msgs/include/**", "/home/<USER>/workcode/rcs_new_ws/install/hit_nav_interfaces/include/**", "/opt/ros/foxy/include/**", "/home/<USER>/workcode/rcs_new_ws/src/rcs_backend_pkg/include/**", "/usr/include/**"], "name": "ROS", "intelliSenseMode": "gcc-x64", "compilerPath": "/usr/bin/gcc", "cStandard": "gnu11", "cppStandard": "c++14"}], "version": 4}