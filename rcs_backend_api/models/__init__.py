"""
Data models for RCS Backend API

This module contains all the data models and enums used in the RCS system.
"""

from .enums import PointProperty, RouteType, RouteProperty, SiteProperty
from .data_types import (
    Point2D,
    PathPoint,
    PathRoute,
    SiteInfo,
    AreaInfo,
    SpecialSite,
    TaskInfo,
    LayerSwitchPoint,
    LayerSwitchConnection,
    ReachablePoint,
    LayerSwitchConnectionInfo
)

__all__ = [
    # Enums
    'PointProperty',
    'RouteType', 
    'RouteProperty',
    'SiteProperty',
    
    # Data Types
    'Point2D',
    'PathPoint',
    'PathRoute',
    'SiteInfo',
    'AreaInfo',
    'SpecialSite',
    'TaskInfo',
    'LayerSwitchPoint',
    'LayerSwitchConnection',
    'ReachablePoint',
    'LayerSwitchConnectionInfo',
]
