"""
Data type definitions for RCS Backend API

This module contains all the data structure definitions used in the RCS system.
"""

from dataclasses import dataclass
from typing import List, Optional
from .enums import PointProperty, RouteType, RouteProperty


@dataclass
class Point2D:
    """2D点结构体"""
    x: float
    y: float


@dataclass
class PathPoint:
    """路径点结构体"""
    id: int
    name: str
    x: float
    y: float
    point_property: PointProperty
    is_rotated_in_place: bool
    floor: int


@dataclass
class PathRoute:
    """路径路线结构体"""
    id: int
    name: str
    type: RouteType
    start: int
    end: int
    c1_x: float
    c1_y: float
    c2_x: float
    c2_y: float
    length: float
    route_property: RouteProperty
    floor: int
    width: float
    movement: bool
    max_speed: float


@dataclass
class SiteInfo:
    """站点信息结构体"""
    id: int
    name: str
    point_id: int
    site_theta: float
    floor: int
    operation: str
    dir: bool
    param1: float
    param2: float
    param3: float
    site_point_x: float
    site_point_y: float
    site_point_valid: bool
    other: str
    layer: int


@dataclass
class AreaInfo:
    """区域信息结构体"""
    id: int
    area_mode: int
    area_points: List[Point2D]
    area_param: int
    floor: int


@dataclass
class SpecialSite:
    """特殊站点结构体"""
    id: int
    name: str
    site_nature: int
    site_point_name: str
    site_theta: float
    floor: int


@dataclass
class TaskInfo:
    """任务信息结构体（包含关联的路径点信息）"""
    # 任务信息（来自site_info表）
    id: int
    name: str
    point_id: int
    site_theta: float
    floor: int
    operation: str
    dir: bool
    param1: float
    param2: float
    param3: float
    site_point_x: float
    site_point_y: float
    site_point_valid: bool
    other: str
    layer: int
    
    # 关联的路径点信息（来自path_point表）
    point_name: str
    point_x: float
    point_y: float
    point_floor: int
    point_property: PointProperty
    point_is_rotated_in_place: bool


@dataclass
class LayerSwitchPoint:
    """楼层切换点结构体"""
    id: int
    name: str
    nature: int
    point_id: int
    floor: int
    in_theta: float
    out_theta: float
    is_active: bool


@dataclass
class LayerSwitchConnection:
    """楼层切换连接结构体"""
    id: int
    from_switch_path_point_id: int
    to_switch_path_point_id: int
    is_active: bool


@dataclass
class ReachablePoint:
    """可到达点结构体"""
    point_id: int
    point_number: int
    in_theta: float
    floor: int


@dataclass
class LayerSwitchConnectionInfo:
    """楼层切换连接信息结构体"""
    # 起始点信息
    source_name: str
    source_point_id: int
    source_point_number: int
    source_floor: int
    source_out_theta: float
    
    # 可到达的点列表
    reachable_points: List[ReachablePoint]
