"""
Database exceptions for RCS Backend API

This module defines custom exceptions for database operations.
"""


class DatabaseError(Exception):
    """Base exception for database operations"""
    pass


class ConnectionError(DatabaseError):
    """Exception raised when database connection fails"""
    pass


class QueryError(DatabaseError):
    """Exception raised when database query fails"""
    pass


class TableCreationError(DatabaseError):
    """Exception raised when table creation fails"""
    pass


class DataValidationError(DatabaseError):
    """Exception raised when data validation fails"""
    pass
