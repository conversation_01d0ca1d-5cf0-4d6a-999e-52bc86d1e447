"""
RCS Backend API Package

A Python package for database operations and API services for the RCS system.
Provides database query methods and REST API endpoints for various RCS data entities.
"""

__version__ = "1.0.0"
__author__ = "RCS Team"

from .database import DatabaseManager
from .models import *
from .api import create_app

__all__ = [
    'DatabaseManager',
    'create_app',
    # Models
    'PathPoint',
    'PathRoute', 
    'SiteInfo',
    'AreaInfo',
    'SpecialSite',
    'TaskInfo',
    'LayerSwitchPoint',
    'LayerSwitchConnection',
    'LayerSwitchConnectionInfo',
    'ReachablePoint',
    'Point2D',
]
